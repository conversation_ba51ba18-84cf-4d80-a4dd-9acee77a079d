# WPS/Excel表格区域截图工具 - 功能说明

## 🎯 工具概述

这是一个专为WPS和Excel用户设计的表格区域截图工具，完全符合您的使用习惯：**在WPS中选择表格区域，然后通过代码帮您把选中的区域保存成图片**。

## 🚀 核心特性

### 1. 三种获取方式
- **剪贴板方式**：在WPS中复制选中区域，程序自动获取
- **COM接口方式**：直接读取WPS/Excel中当前选中的区域
- **屏幕截图方式**：手动选择屏幕区域进行截图

### 2. 全局热键支持
- 设置 `Ctrl+Shift+S` 热键
- 在WPS中选中区域后按热键即可快速截图
- 支持自动保存功能

### 3. 多种输出格式
- PNG：无损压缩，支持透明背景
- JPG：有损压缩，文件较小
- PDF：矢量格式，适合打印

## 📋 使用场景

### 场景一：日常办公（推荐剪贴板方式）
1. 在WPS中打开表格文件
2. 选择需要截图的区域
3. 按 `Ctrl+C` 复制
4. 在截图工具中点击"获取表格数据"
5. 点击"保存图片"

### 场景二：快速截图（推荐热键方式）
1. 启用热键功能
2. 在WPS中选择区域
3. 按 `Ctrl+Shift+S` 即可自动截图保存

### 场景三：批量处理（推荐COM接口方式）
1. 在WPS中依次选择不同区域
2. 每次选择后在工具中点击"获取表格数据"
3. 快速保存多个区域的图片

## 🛠️ 技术优势

### 智能数据解析
- 自动识别表格结构
- 保持原有格式和对齐
- 支持中文字体显示

### 高质量输出
- 可调节DPI（72-600）
- 自定义字体大小
- 可选网格线显示

### 兼容性强
- 支持WPS Office
- 支持Microsoft Excel
- 支持Windows系统

## 📁 文件结构

```
4375873910862955448/
├── excel_to_image.py      # 主程序文件
├── run.py                 # 启动脚本（带依赖检查）
├── 启动程序.bat           # Windows一键启动
├── demo.py                # 功能演示脚本
├── test_functionality.py  # 功能测试脚本
├── requirements.txt       # 依赖库列表
├── README.md             # 详细说明文档
├── 使用指南.md           # 简明使用指南
├── 功能说明.md           # 本文件
├── create_sample_excel.py # 示例文件生成器
└── 示例数据.xlsx         # 示例Excel文件
```

## 🔧 依赖库

### 必需库
- `pandas` - 数据处理
- `Pillow` - 图像处理
- `numpy` - 数值计算

### 可选库（增强功能）
- `pywin32` - COM接口支持（推荐安装）
- `keyboard` - 全局热键支持（推荐安装）

## 🎮 快速开始

### 方法一：一键启动
双击 `启动程序.bat` 文件，程序会自动检查并安装依赖。

### 方法二：命令行启动
```bash
# 安装依赖
pip install -r requirements.txt

# 启动程序
python excel_to_image.py
```

## 💡 使用技巧

### 提高截图质量
- 设置较高的DPI值（300以上）
- 选择合适的字体大小
- 启用网格线显示

### 提高工作效率
- 使用热键功能快速截图
- 启用自动保存减少操作步骤
- 使用COM接口方式避免复制粘贴

### 处理特殊情况
- 如果COM接口不可用，使用剪贴板方式
- 如果表格格式复杂，使用屏幕截图方式
- 如果需要截取非表格内容，使用屏幕截图方式

## 🔍 故障排除

### 常见问题
1. **程序无法启动**
   - 检查Python版本（需要3.7+）
   - 运行 `pip install -r requirements.txt`

2. **COM接口不可用**
   - 安装pywin32：`pip install pywin32`
   - 确保WPS/Excel正在运行

3. **热键不响应**
   - 安装keyboard：`pip install keyboard`
   - 检查是否有其他程序占用热键

4. **中文显示异常**
   - 确保系统安装了中文字体
   - 程序会自动检测并使用合适的字体

## 📞 技术支持

如果遇到问题或有功能建议，请：
1. 查看使用指南和README文档
2. 运行测试脚本检查环境
3. 查看演示脚本了解功能用法

---

**这个工具完全按照您的需求设计：在WPS中选择表格区域，然后代码帮您保存成图片！**
