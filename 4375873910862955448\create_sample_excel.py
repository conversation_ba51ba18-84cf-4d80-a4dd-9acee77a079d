import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
import os

def create_sample_excel():
    """创建示例Excel文件用于测试"""
    
    # 创建工作簿
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "示例数据"
    
    # 设置表头
    headers = ['姓名', '年龄', '部门', '工资', '入职日期']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True, size=12)
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
        cell.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
    
    # 示例数据
    data = [
        ['张三', 28, '技术部', 8000, '2020-01-15'],
        ['李四', 32, '销售部', 7500, '2019-03-20'],
        ['王五', 25, '技术部', 6500, '2021-06-10'],
        ['赵六', 35, '人事部', 9000, '2018-08-05'],
        ['钱七', 29, '财务部', 7000, '2020-11-12'],
        ['孙八', 31, '技术部', 8500, '2019-09-18'],
        ['周九', 27, '销售部', 6800, '2021-02-28'],
        ['吴十', 33, '人事部', 7800, '2018-12-03'],
        ['郑一', 26, '技术部', 6200, '2021-08-15'],
        ['王二', 30, '财务部', 7200, '2020-05-22']
    ]
    
    # 填入数据
    for row, row_data in enumerate(data, 2):
        for col, value in enumerate(row_data, 1):
            cell = ws.cell(row=row, column=col, value=value)
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
    
    # 调整列宽
    column_widths = [10, 8, 12, 10, 15]
    for col, width in enumerate(column_widths, 1):
        ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width
    
    # 创建第二个工作表
    ws2 = wb.create_sheet("销售数据")
    
    # 销售数据表头
    sales_headers = ['月份', '产品A', '产品B', '产品C', '总计']
    for col, header in enumerate(sales_headers, 1):
        cell = ws2.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True, size=12)
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.fill = PatternFill(start_color='FFFF99', end_color='FFFF99', fill_type='solid')
        cell.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
    
    # 销售数据
    sales_data = [
        ['1月', 1200, 800, 600, 2600],
        ['2月', 1500, 900, 700, 3100],
        ['3月', 1800, 1200, 800, 3800],
        ['4月', 1600, 1100, 750, 3450],
        ['5月', 2000, 1300, 900, 4200],
        ['6月', 2200, 1400, 950, 4550]
    ]
    
    # 填入销售数据
    for row, row_data in enumerate(sales_data, 2):
        for col, value in enumerate(row_data, 1):
            cell = ws2.cell(row=row, column=col, value=value)
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
    
    # 调整列宽
    for col in range(1, 6):
        ws2.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 12
    
    # 保存文件
    filename = "示例数据.xlsx"
    wb.save(filename)
    print(f"示例Excel文件已创建: {filename}")
    
    return filename

if __name__ == "__main__":
    create_sample_excel()
