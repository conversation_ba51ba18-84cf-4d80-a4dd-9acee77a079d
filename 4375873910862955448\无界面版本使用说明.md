# 跨平台表格截图工具 - 无界面热键版本

## 🎯 特点

- ✅ **纯跨平台** - 支持Windows、Linux、macOS
- ✅ **无界面** - 后台运行，热键控制
- ✅ **所见即所得** - 保持原始表格样式
- ✅ **简单易用** - 只需按热键即可截图

## 📦 安装依赖

### 通用依赖（所有平台）
```bash
pip install pillow keyboard
```

### 系统特定依赖

**Linux:**
```bash
sudo apt-get install xclip
```

**macOS:**
```bash
brew install pngpaste
```

**Windows:**
无需额外依赖

## 🚀 使用方法

### 1. 启动程序
```bash
python excel_screenshot_hotkey.py
```

### 2. 热键操作

| 热键 | 功能 | 说明 |
|------|------|------|
| `Ctrl+Alt+1` | 剪贴板截图 | 从剪贴板获取图片并保存 |
| `Ctrl+Alt+2` | 手动截图 | 3秒倒计时后截取整个屏幕 |
| `Ctrl+Alt+Q` | 退出程序 | 停止程序运行 |

## 📋 操作流程

### 方式一：剪贴板截图（推荐）

1. **在表格软件中操作**：
   - **Windows**: 在WPS/Excel中选择区域，右键"复制为图片"
   - **Linux**: 在LibreOffice Calc中复制为图片
   - **macOS**: 在Numbers/Excel中复制为图片

2. **按热键保存**：
   - 按 `Ctrl+Alt+1`
   - 图片自动保存到桌面

### 方式二：手动截图

1. **准备截图内容**：
   - 确保要截图的表格在屏幕上可见

2. **按热键截图**：
   - 按 `Ctrl+Alt+2`
   - 等待3秒倒计时
   - 程序自动截取整个屏幕

## 💾 保存设置

- **保存位置**: 桌面 (`~/Desktop`)
- **文件命名**: 
  - 剪贴板: `clipboard_20231201_143022.png`
  - 手动截图: `manual_20231201_143022.png`
- **图片格式**: PNG

## ⚠️ 注意事项

### Linux 系统
- 可能需要root权限运行：`sudo python excel_screenshot_hotkey.py`
- 确保安装了xclip：`sudo apt-get install xclip`

### macOS 系统
- 确保安装了pngpaste：`brew install pngpaste`
- 可能需要授权访问辅助功能

### Windows 系统
- 无特殊要求，直接运行即可

## 🔧 故障排除

### 热键不响应
```bash
# Linux/macOS 尝试使用sudo权限
sudo python excel_screenshot_hotkey.py
```

### 剪贴板功能不工作

**Linux:**
```bash
# 检查xclip是否安装
which xclip
# 如果没有，安装它
sudo apt-get install xclip
```

**macOS:**
```bash
# 检查pngpaste是否安装
which pngpaste
# 如果没有，安装它
brew install pngpaste
```

### 权限问题
```bash
# 给脚本执行权限
chmod +x excel_screenshot_hotkey.py

# 使用管理员权限运行
sudo python excel_screenshot_hotkey.py
```

## 🎉 优势

1. **跨平台兼容** - 一套代码，多平台运行
2. **无界面干扰** - 后台运行，不占用屏幕空间
3. **热键快速** - 按键即用，提高效率
4. **保持原样** - 完全保持表格的原始样式
5. **自动保存** - 无需手动选择保存位置

## 📞 使用技巧

1. **建议使用剪贴板方式** - 更精确，只截取需要的部分
2. **手动截图适合复杂场景** - 可以截取整个屏幕内容
3. **程序可以常驻后台** - 随时按热键使用
4. **文件自动命名** - 包含时间戳，不会覆盖

开始使用吧！🚀
