#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Excel表格区域生成图片工具的基本功能
"""

import sys
import os
import traceback

def test_imports():
    """测试所有必要的库是否能正常导入"""
    print("测试库导入...")

    try:
        import tkinter as tk
        print("✓ tkinter")
    except ImportError as e:
        print(f"✗ tkinter: {e}")
        return False

    try:
        import pandas as pd
        print("✓ pandas")
    except ImportError as e:
        print(f"✗ pandas: {e}")
        return False

    try:
        from PIL import Image, ImageDraw, ImageFont, ImageGrab
        print("✓ Pillow (PIL)")
    except ImportError as e:
        print(f"✗ Pillow: {e}")
        return False

    try:
        import numpy as np
        print("✓ numpy")
    except ImportError as e:
        print(f"✗ numpy: {e}")
        return False

    # 测试可选库
    try:
        import win32com.client
        import win32clipboard
        print("✓ pywin32 (COM接口可用)")
    except ImportError as e:
        print(f"⚠ pywin32: {e} (COM接口不可用)")

    try:
        import keyboard
        print("✓ keyboard (热键功能可用)")
    except ImportError as e:
        print(f"⚠ keyboard: {e} (热键功能不可用)")

    return True

def test_excel_reading():
    """测试Excel文件读取功能"""
    print("\n测试Excel文件读取...")
    
    try:
        import openpyxl
        
        # 检查示例文件是否存在
        excel_file = "示例数据.xlsx"
        if not os.path.exists(excel_file):
            print(f"✗ 示例文件不存在: {excel_file}")
            return False
        
        # 尝试读取Excel文件
        workbook = openpyxl.load_workbook(excel_file, data_only=True)
        print(f"✓ 成功读取Excel文件: {excel_file}")
        
        # 检查工作表
        sheet_names = workbook.sheetnames
        print(f"✓ 工作表: {sheet_names}")
        
        # 读取第一个工作表的数据
        worksheet = workbook[sheet_names[0]]
        max_row = worksheet.max_row
        max_col = worksheet.max_column
        print(f"✓ 数据范围: {max_row}行 x {max_col}列")
        
        # 读取一些示例数据
        for row in range(1, min(4, max_row + 1)):
            row_data = []
            for col in range(1, min(4, max_col + 1)):
                cell = worksheet.cell(row=row, column=col)
                value = cell.value if cell.value is not None else ""
                row_data.append(str(value))
            print(f"  行{row}: {row_data}")
        
        workbook.close()
        return True
        
    except Exception as e:
        print(f"✗ Excel读取失败: {e}")
        traceback.print_exc()
        return False

def test_image_generation():
    """测试图片生成功能"""
    print("\n测试图片生成...")
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        import matplotlib.pyplot as plt
        
        # 测试PIL图片生成
        img = Image.new('RGB', (200, 100), 'white')
        draw = ImageDraw.Draw(img)
        
        try:
            font = ImageFont.load_default()
            draw.text((10, 10), "测试文本", fill='black', font=font)
        except:
            draw.text((10, 10), "测试文本", fill='black')
        
        test_image_path = "test_image.png"
        img.save(test_image_path)
        print(f"✓ PIL图片生成成功: {test_image_path}")
        
        # 清理测试文件
        if os.path.exists(test_image_path):
            os.remove(test_image_path)
        
        # 测试matplotlib
        fig, ax = plt.subplots(figsize=(4, 3))
        ax.text(0.5, 0.5, '测试图表', ha='center', va='center')
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        
        test_plot_path = "test_plot.png"
        fig.savefig(test_plot_path, dpi=150, bbox_inches='tight')
        plt.close(fig)
        print(f"✓ Matplotlib图片生成成功: {test_plot_path}")
        
        # 清理测试文件
        if os.path.exists(test_plot_path):
            os.remove(test_plot_path)
        
        return True
        
    except Exception as e:
        print(f"✗ 图片生成失败: {e}")
        traceback.print_exc()
        return False

def test_main_module():
    """测试主模块是否能正常导入"""
    print("\n测试主模块导入...")

    try:
        # 尝试导入主模块
        from excel_to_image import WPSExcelImageCapture
        print("✓ 主模块导入成功")

        # 测试类是否能正常实例化（不启动GUI）
        print("✓ 主类定义正确")

        return True

    except Exception as e:
        print(f"✗ 主模块导入失败: {e}")
        traceback.print_exc()
        return False

def test_clipboard_functionality():
    """测试剪贴板功能"""
    print("\n测试剪贴板功能...")

    try:
        import tkinter as tk

        # 创建临时tkinter实例测试剪贴板
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口

        # 测试设置和获取剪贴板
        test_data = "测试\t数据\t表格\n第二行\t数据\t内容"
        root.clipboard_clear()
        root.clipboard_append(test_data)

        # 获取剪贴板数据
        clipboard_data = root.clipboard_get()

        if clipboard_data == test_data:
            print("✓ 剪贴板读写功能正常")
            result = True
        else:
            print("✗ 剪贴板数据不匹配")
            result = False

        root.destroy()
        return result

    except Exception as e:
        print(f"✗ 剪贴板功能测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("Excel表格区域生成图片工具 - 功能测试")
    print("=" * 50)
    
    tests = [
        ("库导入测试", test_imports),
        ("Excel读取测试", test_excel_reading),
        ("图片生成测试", test_image_generation),
        ("剪贴板功能测试", test_clipboard_functionality),
        ("主模块测试", test_main_module)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                print(f"✓ {test_name} 通过")
                passed += 1
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！程序应该能正常运行。")
        return True
    else:
        print("✗ 部分测试失败，请检查依赖库安装。")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
