# WPS/Excel表格区域截图工具

这是一个专为WPS和Excel用户设计的表格区域截图工具，支持多种获取方式和输出格式。

## 功能特点

- 📋 **剪贴板方式** - 在WPS/Excel中复制选中区域，程序自动获取并生成图片
- 🔗 **COM接口方式** - 直接与WPS/Excel通信，获取当前选中的区域
- 📸 **屏幕截图方式** - 手动选择屏幕区域进行截图
- ⌨️ **全局热键** - 设置Ctrl+Shift+S热键，随时快速截图
- 🖼️ **多种输出格式** - 支持PNG、JPG、PDF格式输出
- 🎨 **自定义样式** - 可调整字体大小、网格线等
- 🚀 **自动保存** - 支持热键触发后自动保存图片

## 安装依赖

在使用前，请确保安装了所需的依赖库：

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install pandas openpyxl matplotlib Pillow numpy reportlab
```

## 使用方法

### 1. 启动程序

```bash
python excel_to_image.py
```

### 2. 三种使用方式

#### 方式一：剪贴板方式（推荐）
1. 在WPS或Excel中选择要截图的表格区域
2. 按Ctrl+C复制选中区域
3. 在本工具中选择"剪贴板方式"
4. 点击"获取表格数据"
5. 点击"保存图片"选择保存位置

#### 方式二：COM接口方式
1. 确保WPS或Excel正在运行
2. 在WPS/Excel中选择要截图的表格区域
3. 在本工具中选择"COM接口方式"
4. 点击"获取表格数据"（程序会自动获取当前选中区域）
5. 点击"保存图片"

#### 方式三：屏幕截图方式
1. 在本工具中选择"屏幕截图方式"
2. 点击"获取表格数据"
3. 程序会隐藏，屏幕变暗
4. 用鼠标拖拽选择要截图的区域
5. 松开鼠标完成截图

### 3. 热键功能

1. 勾选"启用热键"
2. 在WPS/Excel中选择表格区域
3. 按Ctrl+Shift+S快速截图
4. 如果启用了"自动保存"，图片会自动保存到程序目录

## 支持的文件格式

### 输入格式
- Excel 2007及以上版本 (.xlsx)
- Excel 97-2003 (.xls)

### 输出格式
- **PNG** - 无损压缩，支持透明背景
- **JPG** - 有损压缩，文件较小
- **PDF** - 矢量格式，适合打印

## 样式自定义

### 字体设置
- 支持系统默认字体
- 自动检测中文字体（SimHei）
- 可调整字体大小

### 表格样式
- 网格线开关
- 表头特殊样式
- 单元格对齐方式

### 图片质量
- DPI设置：72-600
- 自动计算最佳尺寸
- 支持高分辨率输出

## 注意事项

1. **文件格式**：建议使用.xlsx格式以获得最佳兼容性
2. **数据量**：大表格可能需要较长处理时间
3. **字体支持**：中文显示需要系统安装相应字体
4. **内存使用**：处理大型表格时注意内存占用

## 常见问题

### Q: 中文显示为方块？
A: 请确保系统安装了中文字体（如SimHei.ttf），或将字体文件放在程序目录下。

### Q: 生成的图片模糊？
A: 提高"图片质量"设置中的DPI值，推荐使用300以上。

### Q: PDF生成失败？
A: 请确保安装了reportlab库：`pip install reportlab`

### Q: 表格显示不完整？
A: 检查选择的区域范围是否正确，确保没有超出工作表边界。

## 技术支持

如果遇到问题或有功能建议，请提交Issue或联系开发者。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的表格区域选择和图片生成
- 支持PNG、JPG、PDF格式输出
- 支持批量处理功能
