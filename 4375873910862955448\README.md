# Excel表格区域生成图片工具

这是一个用于将Excel表格中的指定区域转换为图片的工具，支持多种输出格式和自定义样式。

## 功能特点

- 📊 **可视化表格预览** - 在界面中直接预览Excel表格内容
- 🖱️ **鼠标选择区域** - 支持鼠标拖拽选择表格区域
- ⌨️ **手动输入区域** - 支持手动输入单元格范围（如A1:E10）
- 🖼️ **多种输出格式** - 支持PNG、JPG、PDF格式输出
- 🎨 **自定义样式** - 可调整字体大小、网格线、表头样式等
- 📋 **批量处理** - 支持一次性处理多个表格区域
- 🔍 **实时预览** - 生成前可预览图片效果

## 安装依赖

在使用前，请确保安装了所需的依赖库：

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install pandas openpyxl matplotlib Pillow numpy reportlab
```

## 使用方法

### 1. 启动程序

```bash
python excel_to_image.py
```

### 2. 基本操作流程

1. **选择Excel文件**
   - 点击"选择Excel文件"按钮
   - 选择要处理的.xlsx或.xls文件

2. **选择工作表**
   - 在右上角的下拉菜单中选择要处理的工作表

3. **选择表格区域**
   - **方法一：鼠标选择**
     - 在表格预览区域用鼠标拖拽选择区域
     - 选择的区域会自动显示在右侧面板中
   
   - **方法二：手动输入**
     - 在右侧面板的"起始单元格"和"结束单元格"中输入范围
     - 点击"手动设置"按钮确认

4. **调整设置**
   - **输出格式**：选择PNG、JPG或PDF
   - **图片质量**：设置DPI（72-600）
   - **字体大小**：调整文字大小（8-24）
   - **显示网格线**：是否显示表格边框
   - **包含表头**：是否特殊处理第一行

5. **生成图片**
   - 点击"预览图片"查看效果
   - 点击"生成图片"保存到文件

### 3. 批量处理

1. 点击"批量处理"按钮
2. 在文本框中输入多个区域范围，每行一个，格式如：
   ```
   A1:E10
   G1:K10
   A15:E25
   ```
3. 选择输出文件夹
4. 点击"开始批量处理"

## 支持的文件格式

### 输入格式
- Excel 2007及以上版本 (.xlsx)
- Excel 97-2003 (.xls)

### 输出格式
- **PNG** - 无损压缩，支持透明背景
- **JPG** - 有损压缩，文件较小
- **PDF** - 矢量格式，适合打印

## 样式自定义

### 字体设置
- 支持系统默认字体
- 自动检测中文字体（SimHei）
- 可调整字体大小

### 表格样式
- 网格线开关
- 表头特殊样式
- 单元格对齐方式

### 图片质量
- DPI设置：72-600
- 自动计算最佳尺寸
- 支持高分辨率输出

## 注意事项

1. **文件格式**：建议使用.xlsx格式以获得最佳兼容性
2. **数据量**：大表格可能需要较长处理时间
3. **字体支持**：中文显示需要系统安装相应字体
4. **内存使用**：处理大型表格时注意内存占用

## 常见问题

### Q: 中文显示为方块？
A: 请确保系统安装了中文字体（如SimHei.ttf），或将字体文件放在程序目录下。

### Q: 生成的图片模糊？
A: 提高"图片质量"设置中的DPI值，推荐使用300以上。

### Q: PDF生成失败？
A: 请确保安装了reportlab库：`pip install reportlab`

### Q: 表格显示不完整？
A: 检查选择的区域范围是否正确，确保没有超出工作表边界。

## 技术支持

如果遇到问题或有功能建议，请提交Issue或联系开发者。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的表格区域选择和图片生成
- 支持PNG、JPG、PDF格式输出
- 支持批量处理功能
