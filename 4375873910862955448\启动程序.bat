@echo off
chcp 65001 >nul
title Excel表格区域生成图片工具

echo.
echo ========================================
echo    Excel表格区域生成图片工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python！
    echo 请先安装Python 3.7或更高版本
    echo 下载地址：https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo 检测到Python环境，正在启动程序...
echo.

REM 运行程序
python run.py

REM 如果程序异常退出，显示错误信息
if errorlevel 1 (
    echo.
    echo 程序运行出现错误！
    echo 请检查错误信息并重试
    echo.
)

pause
