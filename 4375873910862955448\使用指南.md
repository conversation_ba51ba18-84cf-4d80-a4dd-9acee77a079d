# WPS/Excel表格区域截图工具 - 使用指南

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 启动程序
```bash
python excel_to_image.py
```

## 三种使用方式详解

### 方式一：剪贴板方式（最简单，推荐）

这是最简单直接的方式，适合大多数用户：

1. **在WPS或Excel中操作**：
   - 打开您的表格文件
   - 用鼠标选择要截图的区域
   - 按 `Ctrl+C` 复制选中区域

2. **在截图工具中操作**：
   - 确保选择了"剪贴板方式"
   - 点击"获取表格数据"按钮
   - 程序会自动解析剪贴板中的表格数据
   - 点击"预览图片"查看效果（可选）
   - 点击"保存图片"选择保存位置

**优点**：简单易用，兼容性好
**缺点**：需要手动复制粘贴

### 方式二：COM接口方式（最智能）

这种方式可以直接与WPS/Excel通信，无需复制粘贴：

1. **前提条件**：
   - 确保WPS或Excel正在运行
   - 确保安装了pywin32库

2. **操作步骤**：
   - 在WPS/Excel中选择要截图的区域（不需要复制）
   - 在截图工具中选择"COM接口方式"
   - 点击"获取表格数据"
   - 程序会自动获取当前选中的区域
   - 点击"保存图片"

**优点**：无需复制，直接获取
**缺点**：需要特定的库支持，可能有兼容性问题

### 方式三：屏幕截图方式（最灵活）

这种方式可以截取屏幕上任意区域：

1. **操作步骤**：
   - 在截图工具中选择"屏幕截图方式"
   - 点击"获取表格数据"
   - 程序窗口会隐藏，屏幕变暗
   - 用鼠标拖拽选择要截图的区域
   - 松开鼠标完成截图
   - 按ESC可以取消截图

**优点**：可以截取任意内容，不限于表格
**缺点**：截取的是图片，无法编辑表格内容

## 热键功能

### 设置热键
1. 勾选"启用热键"选项
2. 热键为：`Ctrl+Shift+S`

### 使用热键
1. 在WPS或Excel中选择表格区域
2. 按 `Ctrl+Shift+S`
3. 程序会根据当前选择的方式自动截图
4. 如果启用了"自动保存"，图片会自动保存到程序目录

### 自动保存
- 勾选"自动保存"选项
- 使用热键时会自动保存图片
- 文件名格式：`auto_capture_时间戳.格式`
- 保存位置：程序运行目录

## 高级功能

### 批量处理
1. 点击"批量处理"按钮
2. 在文本框中输入多个区域，每行一个，格式如：
   ```
   A1:E10
   G1:K10
   A15:E25
   ```
3. 选择输出文件夹
4. 点击"开始批量处理"

### 样式自定义
- **字体设置**：程序会自动检测系统字体，支持中文显示
- **网格线**：可以选择是否显示表格边框
- **表头样式**：第一行可以设置特殊样式
- **图片质量**：高DPI设置适合打印，低DPI适合网页显示

## 文件说明

- `excel_to_image.py` - 主程序文件
- `requirements.txt` - 依赖库列表
- `使用指南.md` - 本文件
- `功能说明.md` - 详细功能说明
- `示例数据.xlsx` - 示例Excel文件

## 常见问题

### Q: 程序无法启动？
A: 请确保安装了Python 3.7或更高版本，并运行 `pip install -r requirements.txt` 安装依赖。

### Q: 中文显示为方块或乱码？
A: 程序已优化中文显示，会自动检测并使用系统中的中文字体（黑体、宋体、微软雅黑等）。如果仍有问题：
- 确保Windows系统字体文件夹中有中文字体
- 重启程序让字体检测生效
- 使用COM接口方式时，确保WPS/Excel中的数据编码正确

### Q: COM接口方式获取数据乱码？
A: 程序已添加智能编码转换，支持UTF-8、GBK等编码。如果仍有问题，建议使用剪贴板方式。

### Q: 生成的图片模糊？
A: 提高"图片质量"设置中的DPI值，推荐使用300以上。

### Q: 无法连接到WPS/Excel？
A: 确保WPS或Excel正在运行，并且已安装pywin32库：`pip install pywin32`

### Q: 热键不响应？
A: 确保已安装keyboard库：`pip install keyboard`，并且没有其他程序占用相同热键。

## 技术特点

- 支持大型Excel文件（自动限制预览行列数以提高性能）
- 智能字体检测（自动选择最佳字体）
- 多格式输出（PNG、JPG、PDF）
- 实时预览功能
- 批量处理支持
- 友好的用户界面

## 更新日志

### v1.0.0 (当前版本)
- 初始版本发布
- 支持Excel文件读取和表格区域选择
- 支持PNG、JPG、PDF格式输出
- 支持批量处理功能
- 支持样式自定义
- 包含完整的用户界面

---

如有问题或建议，请联系开发者。
