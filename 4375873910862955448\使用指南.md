# Excel表格区域生成图片工具 - 使用指南

## 快速开始

### 1. 安装依赖
双击运行 `启动程序.bat` 文件，程序会自动检查并安装所需的依赖库。

或者手动安装：
```bash
pip install -r requirements.txt
```

### 2. 启动程序
- **方法一**：双击 `启动程序.bat`
- **方法二**：运行 `python run.py`
- **方法三**：直接运行 `python excel_to_image.py`

### 3. 基本操作

#### 步骤1：选择Excel文件
1. 点击"选择Excel文件"按钮
2. 选择您要处理的Excel文件（支持.xlsx和.xls格式）
3. 程序会自动加载文件并显示工作表列表

#### 步骤2：选择工作表
在右上角的下拉菜单中选择要处理的工作表

#### 步骤3：选择表格区域
**方法一：鼠标选择**
- 在左侧表格预览区域，用鼠标点击并拖拽选择区域
- 选择的区域会自动显示在右侧面板中

**方法二：手动输入**
- 在右侧"选择区域"面板中输入起始和结束单元格
- 例如：起始单元格 `A1`，结束单元格 `E10`
- 点击"手动设置"按钮确认

#### 步骤4：调整设置
在右侧面板中可以调整：
- **输出格式**：PNG、JPG或PDF
- **图片质量**：DPI设置（72-600）
- **字体大小**：8-24像素
- **显示网格线**：是否显示表格边框
- **包含表头**：是否特殊处理第一行

#### 步骤5：生成图片
1. 点击"预览图片"查看效果（可选）
2. 点击"生成图片"保存到文件
3. 选择保存位置和文件名

## 高级功能

### 批量处理
1. 点击"批量处理"按钮
2. 在文本框中输入多个区域，每行一个，格式如：
   ```
   A1:E10
   G1:K10
   A15:E25
   ```
3. 选择输出文件夹
4. 点击"开始批量处理"

### 样式自定义
- **字体设置**：程序会自动检测系统字体，支持中文显示
- **网格线**：可以选择是否显示表格边框
- **表头样式**：第一行可以设置特殊样式
- **图片质量**：高DPI设置适合打印，低DPI适合网页显示

## 文件说明

- `excel_to_image.py` - 主程序文件
- `run.py` - 启动脚本（带依赖检查）
- `启动程序.bat` - Windows批处理启动文件
- `create_sample_excel.py` - 创建示例Excel文件
- `test_functionality.py` - 功能测试脚本
- `requirements.txt` - 依赖库列表
- `示例数据.xlsx` - 示例Excel文件

## 常见问题

### Q: 程序无法启动？
A: 请确保安装了Python 3.7或更高版本，并运行 `pip install -r requirements.txt` 安装依赖。

### Q: 中文显示为方块？
A: 程序会自动检测中文字体，如果仍有问题，请确保系统安装了中文字体。

### Q: 生成的图片模糊？
A: 提高"图片质量"设置中的DPI值，推荐使用300以上。

### Q: 无法选择表格区域？
A: 确保Excel文件已正确加载，并且工作表中有数据。

### Q: PDF生成失败？
A: 程序会自动尝试使用reportlab库，如果失败会回退到matplotlib生成PDF。

## 技术特点

- 支持大型Excel文件（自动限制预览行列数以提高性能）
- 智能字体检测（自动选择最佳字体）
- 多格式输出（PNG、JPG、PDF）
- 实时预览功能
- 批量处理支持
- 友好的用户界面

## 更新日志

### v1.0.0 (当前版本)
- 初始版本发布
- 支持Excel文件读取和表格区域选择
- 支持PNG、JPG、PDF格式输出
- 支持批量处理功能
- 支持样式自定义
- 包含完整的用户界面

---

如有问题或建议，请联系开发者。
