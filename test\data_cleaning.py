#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据清洗脚本 - 筛选江西省的学校数据
读取Excel文件，保留D列为"江西省"的数据，删除其他行，并保存为CSV文件
"""

import pandas as pd
import os
import sys

def clean_school_data():
    """
    主要的数据清洗函数
    """
    # Excel文件路径
    excel_file = "全国初中高中小学学校名单2020年.xlsx"
    
    # 检查文件是否存在
    if not os.path.exists(excel_file):
        print(f"错误：找不到文件 '{excel_file}'")
        print("请确保Excel文件在当前目录下")
        return False
    
    try:
        print("正在读取Excel文件...")
        # 读取Excel文件
        df = pd.read_excel(excel_file, engine='openpyxl')
        
        print(f"原始数据：共 {len(df)} 行，{len(df.columns)} 列")
        print(f"列名：{list(df.columns)}")
        
        # 显示前几行数据以便确认结构
        print("\n前5行数据预览：")
        print(df.head())
        
        # 检查D列（索引为3）是否存在
        if len(df.columns) < 4:
            print(f"错误：数据只有 {len(df.columns)} 列，无法访问D列")
            return False
        
        # 获取D列的列名
        d_column = df.columns[3]  # D列是第4列，索引为3
        print(f"\nD列列名：'{d_column}'")
        
        # 显示D列的唯一值，了解数据分布
        unique_values = df[d_column].value_counts()
        print(f"\nD列数据分布：")
        print(unique_values.head(10))  # 显示前10个最常见的值
        
        # 检查是否有"江西省"的数据
        jiangxi_count = df[df[d_column] == '江西省'].shape[0]
        print(f"\n江西省的数据行数：{jiangxi_count}")
        
        if jiangxi_count == 0:
            print("警告：没有找到D列为'江西省'的数据")
            # 显示包含"江西"的所有值
            jiangxi_related = df[df[d_column].str.contains('江西', na=False)]
            if len(jiangxi_related) > 0:
                print("找到包含'江西'的数据：")
                print(jiangxi_related[d_column].value_counts())
            return False
        
        # 筛选D列为"江西省"的数据
        print("\n正在筛选江西省的数据...")
        filtered_df = df[df[d_column] == '江西省'].copy()
        
        print(f"筛选后数据：共 {len(filtered_df)} 行，{len(filtered_df.columns)} 列")
        
        # 显示筛选后的前几行数据
        print("\n筛选后前5行数据预览：")
        print(filtered_df.head())
        
        # 保存为CSV文件
        csv_filename = "江西省学校名单_已清洗.csv"
        print(f"\n正在保存为CSV文件：{csv_filename}")
        
        # 保存时使用utf-8-sig编码，确保中文正确显示
        filtered_df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
        
        print(f"✅ 数据清洗完成！")
        print(f"原始数据：{len(df)} 行")
        print(f"筛选后数据：{len(filtered_df)} 行")
        print(f"删除了：{len(df) - len(filtered_df)} 行")
        print(f"CSV文件已保存为：{csv_filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误：{str(e)}")
        print(f"错误类型：{type(e).__name__}")
        return False

def main():
    """
    主函数
    """
    print("=" * 50)
    print("学校数据清洗工具")
    print("筛选江西省的学校数据")
    print("=" * 50)
    
    # 切换到脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"当前工作目录：{os.getcwd()}")
    
    # 执行数据清洗
    success = clean_school_data()
    
    if success:
        print("\n🎉 任务完成！")
    else:
        print("\n❌ 任务失败，请检查错误信息")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
