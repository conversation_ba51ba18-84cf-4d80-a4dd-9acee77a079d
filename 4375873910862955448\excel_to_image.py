import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import pandas as pd
import numpy as np
from PIL import Image, ImageDraw, ImageFont, ImageGrab
import os
import sys
import time
import threading
from datetime import datetime
import io

# 尝试导入COM相关库
try:
    import win32com.client
    import win32clipboard
    import win32con
    WIN32_AVAILABLE = True
    print("✓ win32com 库已加载")
except ImportError:
    WIN32_AVAILABLE = False
    print("⚠ win32com 库未安装，部分功能将不可用")

# 尝试导入热键库
try:
    import keyboard
    KEYBOARD_AVAILABLE = True
    print("✓ keyboard 库已加载")
except ImportError:
    KEYBOARD_AVAILABLE = False
    print("⚠ keyboard 库未安装，热键功能将不可用")

class WPSExcelImageCapture:
    def __init__(self, master):
        self.master = master
        self.master.title("WPS/Excel表格区域截图工具")
        self.master.geometry("600x500")

        # 数据存储
        self.clipboard_data = None
        self.current_app = None
        self.hotkey_enabled = False

        # 创建界面
        self.create_widgets()

        # 设置热键
        if KEYBOARD_AVAILABLE:
            self.setup_hotkeys()
        
    def create_widgets(self):
        """创建主界面"""
        # 主框架
        main_frame = ttk.Frame(self.master)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 标题和说明
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))

        title_label = ttk.Label(title_frame, text="WPS/Excel表格区域截图工具",
                               font=("Arial", 14, "bold"))
        title_label.pack()

        desc_label = ttk.Label(title_frame,
                              text="在WPS或Excel中选择表格区域，然后使用本工具保存为图片",
                              font=("Arial", 9))
        desc_label.pack(pady=(5, 0))

        # 操作方式选择
        method_frame = ttk.LabelFrame(main_frame, text="选择操作方式", padding=10)
        method_frame.pack(fill=tk.X, pady=(0, 10))

        self.method_var = tk.StringVar(value="clipboard")

        ttk.Radiobutton(method_frame, text="剪贴板方式 (推荐)",
                       variable=self.method_var, value="clipboard").pack(anchor=tk.W)
        ttk.Label(method_frame, text="  → 在WPS/Excel中复制选中区域，然后点击下方按钮",
                 font=("Arial", 8), foreground="gray").pack(anchor=tk.W, padx=(20, 0))

        if WIN32_AVAILABLE:
            ttk.Radiobutton(method_frame, text="COM接口方式",
                           variable=self.method_var, value="com").pack(anchor=tk.W, pady=(5, 0))
            ttk.Label(method_frame, text="  → 直接获取当前WPS/Excel中选中的区域",
                     font=("Arial", 8), foreground="gray").pack(anchor=tk.W, padx=(20, 0))

        ttk.Radiobutton(method_frame, text="屏幕截图方式",
                       variable=self.method_var, value="screenshot").pack(anchor=tk.W, pady=(5, 0))
        ttk.Label(method_frame, text="  → 手动选择屏幕区域进行截图",
                 font=("Arial", 8), foreground="gray").pack(anchor=tk.W, padx=(20, 0))

        # 操作按钮
        button_frame = ttk.LabelFrame(main_frame, text="操作", padding=10)
        button_frame.pack(fill=tk.X, pady=(0, 10))

        btn_row1 = ttk.Frame(button_frame)
        btn_row1.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(btn_row1, text="获取表格数据", command=self.capture_table_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_row1, text="预览图片", command=self.preview_image).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_row1, text="保存图片", command=self.save_image).pack(side=tk.LEFT)

        # 设置选项
        self.create_settings_panel(main_frame)

        # 热键设置
        if KEYBOARD_AVAILABLE:
            self.create_hotkey_panel(main_frame)

        # 状态显示
        self.status_var = tk.StringVar(value="就绪 - 请选择操作方式")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_label.pack(fill=tk.X, pady=(10, 0))

    def create_settings_panel(self, parent):
        """创建设置面板"""
        settings_frame = ttk.LabelFrame(parent, text="图片设置", padding=10)
        settings_frame.pack(fill=tk.X, pady=(0, 10))

        # 第一行：格式和质量
        row1 = ttk.Frame(settings_frame)
        row1.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(row1, text="输出格式:").pack(side=tk.LEFT)
        self.format_var = tk.StringVar(value="PNG")
        format_combo = ttk.Combobox(row1, textvariable=self.format_var,
                                   values=["PNG", "JPG", "PDF"], width=8, state="readonly")
        format_combo.pack(side=tk.LEFT, padx=(5, 20))

        ttk.Label(row1, text="图片质量:").pack(side=tk.LEFT)
        self.quality_var = tk.IntVar(value=300)
        quality_spin = ttk.Spinbox(row1, from_=72, to=600, textvariable=self.quality_var, width=8)
        quality_spin.pack(side=tk.LEFT, padx=(5, 0))

        # 第二行：字体和样式
        row2 = ttk.Frame(settings_frame)
        row2.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(row2, text="字体大小:").pack(side=tk.LEFT)
        self.font_size_var = tk.IntVar(value=12)
        ttk.Spinbox(row2, from_=8, to=24, textvariable=self.font_size_var, width=8).pack(side=tk.LEFT, padx=(5, 20))

        self.show_grid_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(row2, text="显示网格线", variable=self.show_grid_var).pack(side=tk.LEFT, padx=(0, 10))

        self.auto_save_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(row2, text="自动保存", variable=self.auto_save_var).pack(side=tk.LEFT)

    def create_hotkey_panel(self, parent):
        """创建热键设置面板"""
        hotkey_frame = ttk.LabelFrame(parent, text="热键设置", padding=10)
        hotkey_frame.pack(fill=tk.X, pady=(0, 10))

        row = ttk.Frame(hotkey_frame)
        row.pack(fill=tk.X)

        self.hotkey_enabled_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(row, text="启用热键", variable=self.hotkey_enabled_var,
                       command=self.toggle_hotkey).pack(side=tk.LEFT)

        ttk.Label(row, text="热键: Ctrl+Shift+S").pack(side=tk.LEFT, padx=(20, 0))
        ttk.Label(row, text="(在WPS/Excel中选中区域后按热键即可保存)",
                 font=("Arial", 8), foreground="gray").pack(side=tk.LEFT, padx=(10, 0))

    def setup_hotkeys(self):
        """设置全局热键"""
        try:
            keyboard.add_hotkey('ctrl+shift+s', self.hotkey_capture)
            print("热键 Ctrl+Shift+S 已设置")
        except Exception as e:
            print(f"设置热键失败: {e}")

    def toggle_hotkey(self):
        """切换热键启用状态"""
        self.hotkey_enabled = self.hotkey_enabled_var.get()
        if self.hotkey_enabled:
            self.status_var.set("热键已启用 - 在WPS/Excel中选中区域后按 Ctrl+Shift+S")
        else:
            self.status_var.set("热键已禁用")

    def hotkey_capture(self):
        """热键触发的截图功能"""
        if not self.hotkey_enabled:
            return

        try:
            # 给用户一点时间切换回WPS/Excel
            time.sleep(0.1)

            # 根据当前设置的方法进行截图
            method = self.method_var.get()
            if method == "clipboard":
                self.capture_from_clipboard()
            elif method == "com" and WIN32_AVAILABLE:
                self.capture_from_com()
            else:
                self.capture_screenshot()

            # 如果启用了自动保存
            if self.auto_save_var.get() and self.clipboard_data:
                self.auto_save_image()

        except Exception as e:
            print(f"热键截图失败: {e}")

    def capture_table_data(self):
        """根据选择的方式获取表格数据"""
        method = self.method_var.get()

        try:
            if method == "clipboard":
                self.capture_from_clipboard()
            elif method == "com" and WIN32_AVAILABLE:
                self.capture_from_com()
            elif method == "screenshot":
                self.capture_screenshot()
            else:
                messagebox.showwarning("警告", "请选择有效的操作方式")

        except Exception as e:
            messagebox.showerror("错误", f"获取数据失败：{str(e)}")
            self.status_var.set("获取数据失败")

    def capture_from_clipboard(self):
        """从剪贴板获取表格数据"""
        try:
            if WIN32_AVAILABLE:
                # 使用win32clipboard获取剪贴板数据
                win32clipboard.OpenClipboard()
                try:
                    # 尝试获取HTML格式的数据（Excel复制时会包含）
                    if win32clipboard.IsClipboardFormatAvailable(win32con.CF_TEXT):
                        text_data = win32clipboard.GetClipboardData(win32con.CF_TEXT)
                        self.parse_clipboard_text(text_data)
                        self.status_var.set("已从剪贴板获取表格数据")
                    else:
                        messagebox.showwarning("警告", "剪贴板中没有找到文本数据，请先在WPS/Excel中复制选中区域")
                finally:
                    win32clipboard.CloseClipboard()
            else:
                # 使用tkinter获取剪贴板数据
                try:
                    text_data = self.master.clipboard_get()
                    self.parse_clipboard_text(text_data)
                    self.status_var.set("已从剪贴板获取表格数据")
                except tk.TclError:
                    messagebox.showwarning("警告", "剪贴板中没有找到文本数据，请先在WPS/Excel中复制选中区域")

        except Exception as e:
            messagebox.showerror("错误", f"从剪贴板获取数据失败：{str(e)}")

    def parse_clipboard_text(self, text_data):
        """解析剪贴板中的文本数据"""
        if not text_data:
            return

        # 按行分割
        lines = text_data.strip().split('\n')

        # 解析表格数据
        table_data = []
        for line in lines:
            # Excel复制的数据通常用制表符分隔
            if '\t' in line:
                row_data = line.split('\t')
            else:
                # 如果没有制表符，尝试其他分隔符
                row_data = [line]
            table_data.append(row_data)

        self.clipboard_data = table_data
        print(f"解析到 {len(table_data)} 行数据")

    def capture_from_com(self):
        """通过COM接口获取当前选中的区域"""
        if not WIN32_AVAILABLE:
            messagebox.showerror("错误", "COM接口不可用，请安装pywin32库")
            return

        try:
            # 尝试连接到Excel
            excel_app = None
            wps_app = None

            try:
                excel_app = win32com.client.GetActiveObject("Excel.Application")
                self.current_app = "Excel"
            except:
                try:
                    wps_app = win32com.client.GetActiveObject("KET.Application")  # WPS的COM接口
                    self.current_app = "WPS"
                except:
                    try:
                        wps_app = win32com.client.GetActiveObject("ET.Application")  # WPS表格的另一个接口
                        self.current_app = "WPS"
                    except:
                        messagebox.showerror("错误", "未找到运行中的Excel或WPS程序")
                        return

            app = excel_app if excel_app else wps_app

            # 获取当前选中的区域
            selection = app.Selection

            if selection is None:
                messagebox.showwarning("警告", "没有选中任何区域")
                return

            # 获取选中区域的数据
            table_data = []

            # 如果选中的是单个单元格
            if hasattr(selection, 'Value'):
                if isinstance(selection.Value, (list, tuple)):
                    # 多行多列
                    for row in selection.Value:
                        if isinstance(row, (list, tuple)):
                            table_data.append([str(cell) if cell is not None else "" for cell in row])
                        else:
                            table_data.append([str(row) if row is not None else ""])
                else:
                    # 单个值
                    table_data.append([str(selection.Value) if selection.Value is not None else ""])

            self.clipboard_data = table_data
            self.status_var.set(f"已从{self.current_app}获取选中区域数据 ({len(table_data)}行)")

        except Exception as e:
            messagebox.showerror("错误", f"COM接口获取数据失败：{str(e)}")

    def capture_screenshot(self):
        """屏幕截图方式"""
        try:
            # 隐藏主窗口
            self.master.withdraw()

            # 等待一下让窗口完全隐藏
            time.sleep(0.5)

            # 创建截图选择窗口
            self.create_screenshot_selector()

        except Exception as e:
            self.master.deiconify()  # 恢复主窗口
            messagebox.showerror("错误", f"屏幕截图失败：{str(e)}")

    def create_screenshot_selector(self):
        """创建截图选择器"""
        # 获取屏幕截图
        screenshot = ImageGrab.grab()

        # 创建全屏窗口用于选择区域
        selector = tk.Toplevel()
        selector.attributes('-fullscreen', True)
        selector.attributes('-alpha', 0.3)
        selector.configure(bg='black')

        # 添加说明文字
        info_label = tk.Label(selector, text="请拖拽鼠标选择要截图的区域，按ESC取消",
                             fg='white', bg='black', font=('Arial', 16))
        info_label.pack(pady=50)

        # 绑定鼠标事件
        self.selection_start = None
        self.selection_end = None
        self.selection_rect = None

        def on_mouse_down(event):
            self.selection_start = (event.x_root, event.y_root)

        def on_mouse_drag(event):
            if self.selection_start:
                self.selection_end = (event.x_root, event.y_root)
                # 这里可以添加实时显示选择框的代码

        def on_mouse_up(event):
            if self.selection_start and self.selection_end:
                # 计算选择区域
                x1, y1 = self.selection_start
                x2, y2 = self.selection_end

                # 确保坐标正确
                left = min(x1, x2)
                top = min(y1, y2)
                right = max(x1, x2)
                bottom = max(y1, y2)

                # 截取选中区域
                region_screenshot = screenshot.crop((left, top, right, bottom))

                # 将截图转换为表格数据格式（这里简化处理）
                self.clipboard_data = [["截图区域"]]  # 占位数据
                self.screenshot_image = region_screenshot

                selector.destroy()
                self.master.deiconify()  # 恢复主窗口
                self.status_var.set(f"已截取屏幕区域 ({right-left}x{bottom-top})")

        def on_escape(event):
            selector.destroy()
            self.master.deiconify()  # 恢复主窗口

        selector.bind('<Button-1>', on_mouse_down)
        selector.bind('<B1-Motion>', on_mouse_drag)
        selector.bind('<ButtonRelease-1>', on_mouse_up)
        selector.bind('<Escape>', on_escape)
        selector.focus_set()

    def preview_image(self):
        """预览生成的图片"""
        if not self.clipboard_data and not hasattr(self, 'screenshot_image'):
            messagebox.showwarning("警告", "请先获取表格数据")
            return

        try:
            # 如果是截图数据，直接显示
            if hasattr(self, 'screenshot_image'):
                self.show_image_preview(self.screenshot_image)
            else:
                # 生成表格图片
                image = self.generate_table_image()
                if image:
                    self.show_image_preview(image)

        except Exception as e:
            messagebox.showerror("错误", f"预览失败：{str(e)}")

    def show_image_preview(self, image):
        """显示图片预览窗口"""
        preview_window = tk.Toplevel(self.master)
        preview_window.title("图片预览")
        preview_window.geometry("800x600")

        # 调整图片大小以适应预览窗口
        display_image = image.copy()
        window_width, window_height = 750, 550
        img_width, img_height = display_image.size

        # 计算缩放比例
        scale = min(window_width/img_width, window_height/img_height)
        if scale < 1:
            new_width = int(img_width * scale)
            new_height = int(img_height * scale)
            display_image = display_image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # 转换为tkinter可显示的格式
        from tkinter import PhotoImage
        import io

        # 将PIL图像转换为PhotoImage
        bio = io.BytesIO()
        display_image.save(bio, format='PNG')
        bio.seek(0)

        # 创建PhotoImage
        photo = PhotoImage(data=bio.getvalue())

        # 显示图片
        label = tk.Label(preview_window, image=photo)
        label.image = photo  # 保持引用
        label.pack(expand=True)

    def generate_table_image(self):
        """根据表格数据生成图片"""
        if not self.clipboard_data:
            return None

        try:
            rows = len(self.clipboard_data)
            cols = max(len(row) for row in self.clipboard_data) if self.clipboard_data else 0

            if rows == 0 or cols == 0:
                return None

            # 计算图片尺寸
            font_size = self.font_size_var.get()
            cell_width = max(120, font_size * 10)
            cell_height = max(40, font_size * 3)

            img_width = cols * cell_width
            img_height = rows * cell_height

            # 创建图片
            image = Image.new('RGB', (img_width, img_height), 'white')
            draw = ImageDraw.Draw(image)

            # 尝试加载字体
            try:
                font = ImageFont.truetype("arial.ttf", font_size)
            except:
                try:
                    font = ImageFont.truetype("simhei.ttf", font_size)  # 中文字体
                except:
                    font = ImageFont.load_default()

            # 绘制表格
            for i, row_data in enumerate(self.clipboard_data):
                for j, cell_value in enumerate(row_data):
                    if j >= cols:  # 防止超出列数
                        break

                    x1 = j * cell_width
                    y1 = i * cell_height
                    x2 = x1 + cell_width
                    y2 = y1 + cell_height

                    # 绘制边框
                    if self.show_grid_var.get():
                        draw.rectangle([x1, y1, x2, y2], outline='black', width=1)

                    # 绘制文本
                    if cell_value and str(cell_value).strip():
                        text = str(cell_value).strip()

                        # 计算文本位置（居中）
                        try:
                            bbox = draw.textbbox((0, 0), text, font=font)
                            text_width = bbox[2] - bbox[0]
                            text_height = bbox[3] - bbox[1]
                        except:
                            # 如果textbbox不可用，使用textsize
                            try:
                                text_width, text_height = draw.textsize(text, font=font)
                            except:
                                text_width, text_height = len(text) * font_size // 2, font_size

                        text_x = x1 + (cell_width - text_width) // 2
                        text_y = y1 + (cell_height - text_height) // 2

                        # 确保文本不超出单元格边界
                        text_x = max(x1 + 2, text_x)
                        text_y = max(y1 + 2, text_y)

                        draw.text((text_x, text_y), text, fill='black', font=font)

            return image

        except Exception as e:
            print(f"生成表格图片失败: {e}")
            return None

    def save_image(self):
        """保存图片"""
        if not self.clipboard_data and not hasattr(self, 'screenshot_image'):
            messagebox.showwarning("警告", "请先获取表格数据")
            return

        try:
            # 生成图片
            if hasattr(self, 'screenshot_image'):
                image = self.screenshot_image
            else:
                image = self.generate_table_image()

            if not image:
                messagebox.showerror("错误", "生成图片失败")
                return

            # 选择保存路径
            file_types = [("PNG图片", "*.png"), ("JPG图片", "*.jpg"), ("PDF文件", "*.pdf")]
            default_ext = self.format_var.get().lower()

            # 生成默认文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"表格截图_{timestamp}.{default_ext}"

            save_path = filedialog.asksaveasfilename(
                title="保存图片",
                initialvalue=default_filename,
                defaultextension=f".{default_ext}",
                filetypes=file_types
            )

            if not save_path:
                return

            # 保存图片
            if self.format_var.get() == "JPG":
                # JPG不支持透明度，需要转换
                if image.mode in ('RGBA', 'LA'):
                    background = Image.new('RGB', image.size, 'white')
                    background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                    image = background
                image.save(save_path, "JPEG", quality=95)
            elif self.format_var.get() == "PDF":
                image.save(save_path, "PDF", quality=95)
            else:
                image.save(save_path, "PNG")

            self.status_var.set(f"图片已保存: {os.path.basename(save_path)}")
            messagebox.showinfo("成功", f"图片已保存到：\n{save_path}")

        except Exception as e:
            messagebox.showerror("错误", f"保存图片失败：{str(e)}")

    def auto_save_image(self):
        """自动保存图片"""
        try:
            if hasattr(self, 'screenshot_image'):
                image = self.screenshot_image
            else:
                image = self.generate_table_image()

            if not image:
                return

            # 生成自动保存的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"auto_capture_{timestamp}.{self.format_var.get().lower()}"

            # 保存到当前目录
            save_path = os.path.join(os.getcwd(), filename)

            if self.format_var.get() == "JPG":
                if image.mode in ('RGBA', 'LA'):
                    background = Image.new('RGB', image.size, 'white')
                    background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                    image = background
                image.save(save_path, "JPEG", quality=95)
            else:
                image.save(save_path, self.format_var.get())

            self.status_var.set(f"已自动保存: {filename}")

        except Exception as e:
            print(f"自动保存失败: {e}")

if __name__ == "__main__":
    root = tk.Tk()
    app = WPSExcelImageCapture(root)
    root.mainloop()
        
    def create_table_view(self, parent):
        """创建表格预览区域"""
        # 创建滚动条
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # 垂直滚动条
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 水平滚动条
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 创建Treeview表格
        self.tree = ttk.Treeview(table_frame, 
                                yscrollcommand=v_scrollbar.set,
                                xscrollcommand=h_scrollbar.set,
                                selectmode='extended')
        self.tree.pack(fill=tk.BOTH, expand=True)
        
        # 配置滚动条
        v_scrollbar.config(command=self.tree.yview)
        h_scrollbar.config(command=self.tree.xview)
        
        # 绑定选择事件
        self.tree.bind('<Button-1>', self.on_table_click)
        self.tree.bind('<B1-Motion>', self.on_table_drag)
        self.tree.bind('<ButtonRelease-1>', self.on_table_release)
        
        # 选择状态
        self.selection_start = None
        self.selection_end = None
        
    def create_control_panel(self, parent):
        """创建控制面板"""
        # 区域选择信息
        range_frame = ttk.LabelFrame(parent, text="选择区域", padding=5)
        range_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(range_frame, text="起始单元格:").grid(row=0, column=0, sticky=tk.W)
        self.start_cell_var = tk.StringVar()
        ttk.Entry(range_frame, textvariable=self.start_cell_var, width=10).grid(row=0, column=1, padx=(5, 0))
        
        ttk.Label(range_frame, text="结束单元格:").grid(row=1, column=0, sticky=tk.W)
        self.end_cell_var = tk.StringVar()
        ttk.Entry(range_frame, textvariable=self.end_cell_var, width=10).grid(row=1, column=1, padx=(5, 0))
        
        ttk.Button(range_frame, text="手动设置", command=self.manual_set_range).grid(row=2, column=0, columnspan=2, pady=5)
        
        # 图片设置
        image_frame = ttk.LabelFrame(parent, text="图片设置", padding=5)
        image_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 输出格式
        ttk.Label(image_frame, text="输出格式:").grid(row=0, column=0, sticky=tk.W)
        self.format_var = tk.StringVar(value="PNG")
        format_combo = ttk.Combobox(image_frame, textvariable=self.format_var, 
                                   values=["PNG", "JPG", "PDF"], width=8, state="readonly")
        format_combo.grid(row=0, column=1, padx=(5, 0))
        
        # 图片质量
        ttk.Label(image_frame, text="图片质量:").grid(row=1, column=0, sticky=tk.W)
        self.quality_var = tk.IntVar(value=300)
        quality_spin = ttk.Spinbox(image_frame, from_=72, to=600, textvariable=self.quality_var, width=8)
        quality_spin.grid(row=1, column=1, padx=(5, 0))
        
        # 样式设置
        style_frame = ttk.LabelFrame(parent, text="样式设置", padding=5)
        style_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 字体大小
        ttk.Label(style_frame, text="字体大小:").grid(row=0, column=0, sticky=tk.W)
        self.font_size_var = tk.IntVar(value=12)
        ttk.Spinbox(style_frame, from_=8, to=24, textvariable=self.font_size_var, width=8).grid(row=0, column=1, padx=(5, 0))
        
        # 是否显示网格线
        self.show_grid_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(style_frame, text="显示网格线", variable=self.show_grid_var).grid(row=1, column=0, columnspan=2, sticky=tk.W)
        
        # 是否包含表头
        self.include_header_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(style_frame, text="包含表头", variable=self.include_header_var).grid(row=2, column=0, columnspan=2, sticky=tk.W)
        
        # 操作按钮
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(button_frame, text="预览图片", command=self.preview_image).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="生成图片", command=self.generate_image).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="批量处理", command=self.batch_process).pack(fill=tk.X)
        
        # 状态显示
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(parent, textvariable=self.status_var, relief=tk.SUNKEN)
        status_label.pack(fill=tk.X, pady=(10, 0))

    def select_excel_file(self):
        """选择Excel文件"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.excel_file_path = file_path
            self.file_label.config(text=os.path.basename(file_path))
            self.load_excel_file()
            
    def load_excel_file(self):
        """加载Excel文件"""
        try:
            self.status_var.set("正在加载Excel文件...")
            self.workbook = openpyxl.load_workbook(self.excel_file_path, data_only=True)
            
            # 更新工作表下拉列表
            sheet_names = self.workbook.sheetnames
            self.sheet_combo['values'] = sheet_names
            if sheet_names:
                self.sheet_combo.set(sheet_names[0])
                self.load_worksheet()
                
            self.status_var.set("Excel文件加载完成")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载Excel文件失败：{str(e)}")
            self.status_var.set("加载失败")

    def load_worksheet(self, event=None):
        """加载选定的工作表"""
        if not self.workbook:
            return
            
        try:
            sheet_name = self.sheet_combo.get()
            self.worksheet = self.workbook[sheet_name]
            self.display_table_data()
            self.status_var.set(f"已加载工作表: {sheet_name}")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载工作表失败：{str(e)}")

    def display_table_data(self):
        """显示表格数据"""
        if not self.worksheet:
            return

        try:
            # 清空现有数据
            for item in self.tree.get_children():
                self.tree.delete(item)

            # 获取数据范围
            max_row = self.worksheet.max_row
            max_col = self.worksheet.max_column

            if max_row == 0 or max_col == 0:
                return

            # 设置列标题
            columns = []
            for col in range(1, min(max_col + 1, 26)):  # 限制显示列数
                col_letter = openpyxl.utils.get_column_letter(col)
                columns.append(col_letter)

            self.tree['columns'] = columns
            self.tree['show'] = 'tree headings'

            # 设置列标题
            self.tree.heading('#0', text='行号')
            self.tree.column('#0', width=50)

            for col in columns:
                self.tree.heading(col, text=col)
                self.tree.column(col, width=80)

            # 添加数据行
            for row in range(1, min(max_row + 1, 101)):  # 限制显示行数
                values = []
                for col in range(1, len(columns) + 1):
                    cell = self.worksheet.cell(row=row, column=col)
                    value = cell.value if cell.value is not None else ""
                    values.append(str(value))

                self.tree.insert('', 'end', text=str(row), values=values)

        except Exception as e:
            messagebox.showerror("错误", f"显示表格数据失败：{str(e)}")

    def on_table_click(self, event):
        """处理表格点击事件"""
        item = self.tree.identify('item', event.x, event.y)
        column = self.tree.identify('column', event.x, event.y)

        if item and column:
            row_num = self.tree.item(item, 'text')
            if column == '#0':
                col_num = 1
            else:
                col_num = self.tree['columns'].index(column) + 1

            self.selection_start = (int(row_num), col_num)
            self.selection_end = self.selection_start
            self.update_selection_display()

    def on_table_drag(self, event):
        """处理表格拖拽事件"""
        if not self.selection_start:
            return

        item = self.tree.identify('item', event.x, event.y)
        column = self.tree.identify('column', event.x, event.y)

        if item and column:
            row_num = self.tree.item(item, 'text')
            if column == '#0':
                col_num = 1
            else:
                col_num = self.tree['columns'].index(column) + 1

            self.selection_end = (int(row_num), col_num)
            self.update_selection_display()

    def on_table_release(self, event):
        """处理表格释放事件"""
        if self.selection_start and self.selection_end:
            self.finalize_selection()

    def update_selection_display(self):
        """更新选择区域显示"""
        if not self.selection_start or not self.selection_end:
            return

        start_row, start_col = self.selection_start
        end_row, end_col = self.selection_end

        # 确保起始位置在左上角
        min_row = min(start_row, end_row)
        max_row = max(start_row, end_row)
        min_col = min(start_col, end_col)
        max_col = max(start_col, end_col)

        start_cell = f"{openpyxl.utils.get_column_letter(min_col)}{min_row}"
        end_cell = f"{openpyxl.utils.get_column_letter(max_col)}{max_row}"

        self.start_cell_var.set(start_cell)
        self.end_cell_var.set(end_cell)

    def finalize_selection(self):
        """确定选择区域"""
        if not self.selection_start or not self.selection_end:
            return

        start_row, start_col = self.selection_start
        end_row, end_col = self.selection_end

        # 确保起始位置在左上角
        min_row = min(start_row, end_row)
        max_row = max(start_row, end_row)
        min_col = min(start_col, end_col)
        max_col = max(start_col, end_col)

        self.selected_range = {
            'start_row': min_row,
            'end_row': max_row,
            'start_col': min_col,
            'end_col': max_col
        }

        self.status_var.set(f"已选择区域: {self.start_cell_var.get()}:{self.end_cell_var.get()}")

    def manual_set_range(self):
        """手动设置选择区域"""
        try:
            start_cell = self.start_cell_var.get().strip().upper()
            end_cell = self.end_cell_var.get().strip().upper()

            if not start_cell or not end_cell:
                messagebox.showwarning("警告", "请输入起始和结束单元格")
                return

            # 解析单元格地址
            start_coord = openpyxl.utils.coordinate_from_string(start_cell)
            end_coord = openpyxl.utils.coordinate_from_string(end_cell)

            start_col = openpyxl.utils.column_index_from_string(start_coord[0])
            start_row = start_coord[1]
            end_col = openpyxl.utils.column_index_from_string(end_coord[0])
            end_row = end_coord[1]

            self.selected_range = {
                'start_row': min(start_row, end_row),
                'end_row': max(start_row, end_row),
                'start_col': min(start_col, end_col),
                'end_col': max(start_col, end_col)
            }

            self.status_var.set(f"手动设置区域: {start_cell}:{end_cell}")

        except Exception as e:
            messagebox.showerror("错误", f"无效的单元格地址：{str(e)}")

    def get_selected_data(self):
        """获取选定区域的数据"""
        if not self.worksheet or not self.selected_range:
            return None

        try:
            data = []
            for row in range(self.selected_range['start_row'], self.selected_range['end_row'] + 1):
                row_data = []
                for col in range(self.selected_range['start_col'], self.selected_range['end_col'] + 1):
                    cell = self.worksheet.cell(row=row, column=col)
                    value = cell.value if cell.value is not None else ""
                    row_data.append(str(value))
                data.append(row_data)
            return data

        except Exception as e:
            messagebox.showerror("错误", f"获取数据失败：{str(e)}")
            return None

    def preview_image(self):
        """预览图片"""
        if not self.selected_range:
            messagebox.showwarning("警告", "请先选择表格区域")
            return

        data = self.get_selected_data()
        if not data:
            return

        # 创建预览窗口
        preview_window = tk.Toplevel(self.master)
        preview_window.title("图片预览")
        preview_window.geometry("800x600")

        # 创建matplotlib图形
        fig, ax = plt.subplots(figsize=(10, 6))
        self.create_table_image(ax, data)

        # 嵌入到tkinter窗口
        canvas = FigureCanvasTkAgg(fig, preview_window)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def create_table_image(self, ax, data):
        """创建表格图片"""
        if not data:
            return

        rows = len(data)
        cols = len(data[0]) if data else 0

        # 清除坐标轴
        ax.set_xlim(0, cols)
        ax.set_ylim(0, rows)
        ax.set_aspect('equal')

        # 隐藏坐标轴
        ax.axis('off')

        # 绘制表格
        cell_height = 1
        cell_width = 1

        for i, row_data in enumerate(data):
            for j, cell_value in enumerate(row_data):
                # 计算单元格位置
                x = j * cell_width
                y = (rows - i - 1) * cell_height

                # 绘制单元格边框
                if self.show_grid_var.get():
                    rect = patches.Rectangle((x, y), cell_width, cell_height,
                                           linewidth=1, edgecolor='black', facecolor='white')
                    ax.add_patch(rect)

                # 添加文本
                if cell_value:
                    ax.text(x + cell_width/2, y + cell_height/2, str(cell_value),
                           ha='center', va='center', fontsize=self.font_size_var.get(),
                           wrap=True)

    def generate_image(self):
        """生成图片文件"""
        if not self.selected_range:
            messagebox.showwarning("警告", "请先选择表格区域")
            return

        data = self.get_selected_data()
        if not data:
            return

        # 选择保存路径
        file_types = [("PNG图片", "*.png"), ("JPG图片", "*.jpg"), ("PDF文件", "*.pdf")]
        default_ext = self.format_var.get().lower()

        save_path = filedialog.asksaveasfilename(
            title="保存图片",
            defaultextension=f".{default_ext}",
            filetypes=file_types
        )

        if not save_path:
            return

        try:
            self.status_var.set("正在生成图片...")

            # 创建图片
            if self.format_var.get() == "PDF":
                self.generate_pdf_image(data, save_path)
            else:
                self.generate_raster_image(data, save_path)

            self.status_var.set("图片生成完成")
            messagebox.showinfo("成功", f"图片已保存到：{save_path}")

        except Exception as e:
            messagebox.showerror("错误", f"生成图片失败：{str(e)}")
            self.status_var.set("生成失败")

    def generate_raster_image(self, data, save_path):
        """生成栅格图片（PNG/JPG）"""
        if not data:
            return

        rows = len(data)
        cols = len(data[0]) if data else 0

        # 计算图片尺寸
        font_size = self.font_size_var.get()
        cell_width = max(100, font_size * 8)
        cell_height = max(30, font_size * 2)

        img_width = cols * cell_width
        img_height = rows * cell_height

        # 创建图片
        img = Image.new('RGB', (img_width, img_height), 'white')
        draw = ImageDraw.Draw(img)

        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            try:
                font = ImageFont.truetype("simhei.ttf", font_size)  # 中文字体
            except:
                font = ImageFont.load_default()

        # 绘制表格
        for i, row_data in enumerate(data):
            for j, cell_value in enumerate(row_data):
                x1 = j * cell_width
                y1 = i * cell_height
                x2 = x1 + cell_width
                y2 = y1 + cell_height

                # 绘制边框
                if self.show_grid_var.get():
                    draw.rectangle([x1, y1, x2, y2], outline='black', width=1)

                # 绘制文本
                if cell_value:
                    text_x = x1 + cell_width // 2
                    text_y = y1 + cell_height // 2

                    # 获取文本尺寸
                    bbox = draw.textbbox((0, 0), str(cell_value), font=font)
                    text_width = bbox[2] - bbox[0]
                    text_height = bbox[3] - bbox[1]

                    # 居中显示
                    draw.text((text_x - text_width//2, text_y - text_height//2),
                             str(cell_value), fill='black', font=font)

        # 保存图片
        if self.format_var.get() == "JPG":
            img.save(save_path, "JPEG", quality=95)
        else:
            img.save(save_path, "PNG")

    def generate_pdf_image(self, data, save_path):
        """生成PDF图片"""
        try:
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Table, TableStyle
            from reportlab.lib import colors
            from reportlab.lib.units import inch
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont

            # 注册中文字体
            try:
                pdfmetrics.registerFont(TTFont('SimHei', 'simhei.ttf'))
                font_name = 'SimHei'
            except:
                font_name = 'Helvetica'

            # 创建PDF文档
            doc = SimpleDocTemplate(save_path, pagesize=A4)
            elements = []

            # 创建表格
            table = Table(data)

            # 设置表格样式
            style = TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), font_name),
                ('FONTSIZE', (0, 0), (-1, -1), self.font_size_var.get()),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('BACKGROUND', (0, 0), (-1, -1), colors.white),
            ])

            if self.show_grid_var.get():
                style.add('GRID', (0, 0), (-1, -1), 1, colors.black)

            if self.include_header_var.get() and len(data) > 0:
                style.add('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey)
                style.add('FONTNAME', (0, 0), (-1, 0), font_name)
                style.add('FONTSIZE', (0, 0), (-1, 0), self.font_size_var.get() + 2)

            table.setStyle(style)
            elements.append(table)

            # 生成PDF
            doc.build(elements)

        except ImportError:
            # 如果没有reportlab，使用matplotlib生成PDF
            fig, ax = plt.subplots(figsize=(11, 8.5))
            self.create_table_image(ax, data)
            fig.savefig(save_path, format='pdf', bbox_inches='tight', dpi=self.quality_var.get())
            plt.close(fig)

    def batch_process(self):
        """批量处理功能"""
        if not self.worksheet:
            messagebox.showwarning("警告", "请先加载Excel文件")
            return

        # 创建批量处理窗口
        batch_window = tk.Toplevel(self.master)
        batch_window.title("批量处理")
        batch_window.geometry("500x400")

        # 批量设置区域
        settings_frame = ttk.LabelFrame(batch_window, text="批量设置", padding=10)
        settings_frame.pack(fill=tk.X, padx=10, pady=10)

        # 区域列表
        ttk.Label(settings_frame, text="要处理的区域（每行一个，格式：A1:C5）:").pack(anchor=tk.W)

        ranges_text = tk.Text(settings_frame, height=8, width=50)
        ranges_text.pack(fill=tk.BOTH, expand=True, pady=5)

        # 示例文本
        example_text = """A1:E10
G1:K10
A15:E25"""
        ranges_text.insert(tk.END, example_text)

        # 输出设置
        output_frame = ttk.LabelFrame(batch_window, text="输出设置", padding=10)
        output_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(output_frame, text="输出文件夹:").pack(anchor=tk.W)

        folder_frame = ttk.Frame(output_frame)
        folder_frame.pack(fill=tk.X, pady=5)

        output_folder_var = tk.StringVar()
        ttk.Entry(folder_frame, textvariable=output_folder_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(folder_frame, text="浏览",
                  command=lambda: output_folder_var.set(filedialog.askdirectory())).pack(side=tk.RIGHT, padx=(5, 0))

        # 处理按钮
        def process_batch():
            ranges_text_content = ranges_text.get(1.0, tk.END).strip()
            output_folder = output_folder_var.get().strip()

            if not ranges_text_content or not output_folder:
                messagebox.showwarning("警告", "请填写所有必要信息")
                return

            ranges = [r.strip() for r in ranges_text_content.split('\n') if r.strip()]

            try:
                for i, range_str in enumerate(ranges):
                    # 解析范围
                    if ':' not in range_str:
                        continue

                    start_cell, end_cell = range_str.split(':')

                    # 临时设置选择区域
                    self.start_cell_var.set(start_cell.strip())
                    self.end_cell_var.set(end_cell.strip())
                    self.manual_set_range()

                    # 获取数据
                    data = self.get_selected_data()
                    if not data:
                        continue

                    # 生成文件名
                    filename = f"table_{i+1}_{range_str.replace(':', '_')}.{self.format_var.get().lower()}"
                    save_path = os.path.join(output_folder, filename)

                    # 生成图片
                    if self.format_var.get() == "PDF":
                        self.generate_pdf_image(data, save_path)
                    else:
                        self.generate_raster_image(data, save_path)

                messagebox.showinfo("成功", f"批量处理完成！共处理 {len(ranges)} 个区域")
                batch_window.destroy()

            except Exception as e:
                messagebox.showerror("错误", f"批量处理失败：{str(e)}")

        ttk.Button(batch_window, text="开始批量处理", command=process_batch).pack(pady=10)

if __name__ == "__main__":
    root = tk.Tk()
    app = ExcelToImageConverter(root)
    root.mainloop()
