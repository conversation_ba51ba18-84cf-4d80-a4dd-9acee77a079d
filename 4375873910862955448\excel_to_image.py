import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import os
import sys
from datetime import datetime

class ExcelToImageConverter:
    def __init__(self, master):
        self.master = master
        self.master.title("Excel表格区域生成图片工具")
        self.master.geometry("1200x800")
        
        # 数据存储
        self.excel_file_path = None
        self.workbook = None
        self.worksheet = None
        self.selected_range = None
        self.table_data = None
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        """创建主界面"""
        # 主框架
        main_frame = ttk.Frame(self.master)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding=10)
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(file_frame, text="选择Excel文件", command=self.select_excel_file).pack(side=tk.LEFT)
        self.file_label = ttk.Label(file_frame, text="未选择文件")
        self.file_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # 工作表选择
        sheet_frame = ttk.Frame(file_frame)
        sheet_frame.pack(side=tk.RIGHT)
        ttk.Label(sheet_frame, text="工作表:").pack(side=tk.LEFT)
        self.sheet_combo = ttk.Combobox(sheet_frame, width=15, state="readonly")
        self.sheet_combo.pack(side=tk.LEFT, padx=(5, 0))
        self.sheet_combo.bind("<<ComboboxSelected>>", self.load_worksheet)
        
        # 内容区域
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧：表格预览
        left_frame = ttk.LabelFrame(content_frame, text="表格预览", padding=5)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 表格显示区域
        self.create_table_view(left_frame)
        
        # 右侧：控制面板
        right_frame = ttk.LabelFrame(content_frame, text="设置选项", padding=10)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        right_frame.config(width=300)
        
        self.create_control_panel(right_frame)
        
    def create_table_view(self, parent):
        """创建表格预览区域"""
        # 创建滚动条
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # 垂直滚动条
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 水平滚动条
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 创建Treeview表格
        self.tree = ttk.Treeview(table_frame, 
                                yscrollcommand=v_scrollbar.set,
                                xscrollcommand=h_scrollbar.set,
                                selectmode='extended')
        self.tree.pack(fill=tk.BOTH, expand=True)
        
        # 配置滚动条
        v_scrollbar.config(command=self.tree.yview)
        h_scrollbar.config(command=self.tree.xview)
        
        # 绑定选择事件
        self.tree.bind('<Button-1>', self.on_table_click)
        self.tree.bind('<B1-Motion>', self.on_table_drag)
        self.tree.bind('<ButtonRelease-1>', self.on_table_release)
        
        # 选择状态
        self.selection_start = None
        self.selection_end = None
        
    def create_control_panel(self, parent):
        """创建控制面板"""
        # 区域选择信息
        range_frame = ttk.LabelFrame(parent, text="选择区域", padding=5)
        range_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(range_frame, text="起始单元格:").grid(row=0, column=0, sticky=tk.W)
        self.start_cell_var = tk.StringVar()
        ttk.Entry(range_frame, textvariable=self.start_cell_var, width=10).grid(row=0, column=1, padx=(5, 0))
        
        ttk.Label(range_frame, text="结束单元格:").grid(row=1, column=0, sticky=tk.W)
        self.end_cell_var = tk.StringVar()
        ttk.Entry(range_frame, textvariable=self.end_cell_var, width=10).grid(row=1, column=1, padx=(5, 0))
        
        ttk.Button(range_frame, text="手动设置", command=self.manual_set_range).grid(row=2, column=0, columnspan=2, pady=5)
        
        # 图片设置
        image_frame = ttk.LabelFrame(parent, text="图片设置", padding=5)
        image_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 输出格式
        ttk.Label(image_frame, text="输出格式:").grid(row=0, column=0, sticky=tk.W)
        self.format_var = tk.StringVar(value="PNG")
        format_combo = ttk.Combobox(image_frame, textvariable=self.format_var, 
                                   values=["PNG", "JPG", "PDF"], width=8, state="readonly")
        format_combo.grid(row=0, column=1, padx=(5, 0))
        
        # 图片质量
        ttk.Label(image_frame, text="图片质量:").grid(row=1, column=0, sticky=tk.W)
        self.quality_var = tk.IntVar(value=300)
        quality_spin = ttk.Spinbox(image_frame, from_=72, to=600, textvariable=self.quality_var, width=8)
        quality_spin.grid(row=1, column=1, padx=(5, 0))
        
        # 样式设置
        style_frame = ttk.LabelFrame(parent, text="样式设置", padding=5)
        style_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 字体大小
        ttk.Label(style_frame, text="字体大小:").grid(row=0, column=0, sticky=tk.W)
        self.font_size_var = tk.IntVar(value=12)
        ttk.Spinbox(style_frame, from_=8, to=24, textvariable=self.font_size_var, width=8).grid(row=0, column=1, padx=(5, 0))
        
        # 是否显示网格线
        self.show_grid_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(style_frame, text="显示网格线", variable=self.show_grid_var).grid(row=1, column=0, columnspan=2, sticky=tk.W)
        
        # 是否包含表头
        self.include_header_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(style_frame, text="包含表头", variable=self.include_header_var).grid(row=2, column=0, columnspan=2, sticky=tk.W)
        
        # 操作按钮
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(button_frame, text="预览图片", command=self.preview_image).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="生成图片", command=self.generate_image).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="批量处理", command=self.batch_process).pack(fill=tk.X)
        
        # 状态显示
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(parent, textvariable=self.status_var, relief=tk.SUNKEN)
        status_label.pack(fill=tk.X, pady=(10, 0))

    def select_excel_file(self):
        """选择Excel文件"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.excel_file_path = file_path
            self.file_label.config(text=os.path.basename(file_path))
            self.load_excel_file()
            
    def load_excel_file(self):
        """加载Excel文件"""
        try:
            self.status_var.set("正在加载Excel文件...")
            self.workbook = openpyxl.load_workbook(self.excel_file_path, data_only=True)
            
            # 更新工作表下拉列表
            sheet_names = self.workbook.sheetnames
            self.sheet_combo['values'] = sheet_names
            if sheet_names:
                self.sheet_combo.set(sheet_names[0])
                self.load_worksheet()
                
            self.status_var.set("Excel文件加载完成")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载Excel文件失败：{str(e)}")
            self.status_var.set("加载失败")

    def load_worksheet(self, event=None):
        """加载选定的工作表"""
        if not self.workbook:
            return
            
        try:
            sheet_name = self.sheet_combo.get()
            self.worksheet = self.workbook[sheet_name]
            self.display_table_data()
            self.status_var.set(f"已加载工作表: {sheet_name}")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载工作表失败：{str(e)}")

    def display_table_data(self):
        """显示表格数据"""
        if not self.worksheet:
            return

        try:
            # 清空现有数据
            for item in self.tree.get_children():
                self.tree.delete(item)

            # 获取数据范围
            max_row = self.worksheet.max_row
            max_col = self.worksheet.max_column

            if max_row == 0 or max_col == 0:
                return

            # 设置列标题
            columns = []
            for col in range(1, min(max_col + 1, 26)):  # 限制显示列数
                col_letter = openpyxl.utils.get_column_letter(col)
                columns.append(col_letter)

            self.tree['columns'] = columns
            self.tree['show'] = 'tree headings'

            # 设置列标题
            self.tree.heading('#0', text='行号')
            self.tree.column('#0', width=50)

            for col in columns:
                self.tree.heading(col, text=col)
                self.tree.column(col, width=80)

            # 添加数据行
            for row in range(1, min(max_row + 1, 101)):  # 限制显示行数
                values = []
                for col in range(1, len(columns) + 1):
                    cell = self.worksheet.cell(row=row, column=col)
                    value = cell.value if cell.value is not None else ""
                    values.append(str(value))

                self.tree.insert('', 'end', text=str(row), values=values)

        except Exception as e:
            messagebox.showerror("错误", f"显示表格数据失败：{str(e)}")

    def on_table_click(self, event):
        """处理表格点击事件"""
        item = self.tree.identify('item', event.x, event.y)
        column = self.tree.identify('column', event.x, event.y)

        if item and column:
            row_num = self.tree.item(item, 'text')
            if column == '#0':
                col_num = 1
            else:
                col_num = self.tree['columns'].index(column) + 1

            self.selection_start = (int(row_num), col_num)
            self.selection_end = self.selection_start
            self.update_selection_display()

    def on_table_drag(self, event):
        """处理表格拖拽事件"""
        if not self.selection_start:
            return

        item = self.tree.identify('item', event.x, event.y)
        column = self.tree.identify('column', event.x, event.y)

        if item and column:
            row_num = self.tree.item(item, 'text')
            if column == '#0':
                col_num = 1
            else:
                col_num = self.tree['columns'].index(column) + 1

            self.selection_end = (int(row_num), col_num)
            self.update_selection_display()

    def on_table_release(self, event):
        """处理表格释放事件"""
        if self.selection_start and self.selection_end:
            self.finalize_selection()

    def update_selection_display(self):
        """更新选择区域显示"""
        if not self.selection_start or not self.selection_end:
            return

        start_row, start_col = self.selection_start
        end_row, end_col = self.selection_end

        # 确保起始位置在左上角
        min_row = min(start_row, end_row)
        max_row = max(start_row, end_row)
        min_col = min(start_col, end_col)
        max_col = max(start_col, end_col)

        start_cell = f"{openpyxl.utils.get_column_letter(min_col)}{min_row}"
        end_cell = f"{openpyxl.utils.get_column_letter(max_col)}{max_row}"

        self.start_cell_var.set(start_cell)
        self.end_cell_var.set(end_cell)

    def finalize_selection(self):
        """确定选择区域"""
        if not self.selection_start or not self.selection_end:
            return

        start_row, start_col = self.selection_start
        end_row, end_col = self.selection_end

        # 确保起始位置在左上角
        min_row = min(start_row, end_row)
        max_row = max(start_row, end_row)
        min_col = min(start_col, end_col)
        max_col = max(start_col, end_col)

        self.selected_range = {
            'start_row': min_row,
            'end_row': max_row,
            'start_col': min_col,
            'end_col': max_col
        }

        self.status_var.set(f"已选择区域: {self.start_cell_var.get()}:{self.end_cell_var.get()}")

    def manual_set_range(self):
        """手动设置选择区域"""
        try:
            start_cell = self.start_cell_var.get().strip().upper()
            end_cell = self.end_cell_var.get().strip().upper()

            if not start_cell or not end_cell:
                messagebox.showwarning("警告", "请输入起始和结束单元格")
                return

            # 解析单元格地址
            start_coord = openpyxl.utils.coordinate_from_string(start_cell)
            end_coord = openpyxl.utils.coordinate_from_string(end_cell)

            start_col = openpyxl.utils.column_index_from_string(start_coord[0])
            start_row = start_coord[1]
            end_col = openpyxl.utils.column_index_from_string(end_coord[0])
            end_row = end_coord[1]

            self.selected_range = {
                'start_row': min(start_row, end_row),
                'end_row': max(start_row, end_row),
                'start_col': min(start_col, end_col),
                'end_col': max(start_col, end_col)
            }

            self.status_var.set(f"手动设置区域: {start_cell}:{end_cell}")

        except Exception as e:
            messagebox.showerror("错误", f"无效的单元格地址：{str(e)}")

    def get_selected_data(self):
        """获取选定区域的数据"""
        if not self.worksheet or not self.selected_range:
            return None

        try:
            data = []
            for row in range(self.selected_range['start_row'], self.selected_range['end_row'] + 1):
                row_data = []
                for col in range(self.selected_range['start_col'], self.selected_range['end_col'] + 1):
                    cell = self.worksheet.cell(row=row, column=col)
                    value = cell.value if cell.value is not None else ""
                    row_data.append(str(value))
                data.append(row_data)
            return data

        except Exception as e:
            messagebox.showerror("错误", f"获取数据失败：{str(e)}")
            return None

    def preview_image(self):
        """预览图片"""
        if not self.selected_range:
            messagebox.showwarning("警告", "请先选择表格区域")
            return

        data = self.get_selected_data()
        if not data:
            return

        # 创建预览窗口
        preview_window = tk.Toplevel(self.master)
        preview_window.title("图片预览")
        preview_window.geometry("800x600")

        # 创建matplotlib图形
        fig, ax = plt.subplots(figsize=(10, 6))
        self.create_table_image(ax, data)

        # 嵌入到tkinter窗口
        canvas = FigureCanvasTkAgg(fig, preview_window)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def create_table_image(self, ax, data):
        """创建表格图片"""
        if not data:
            return

        rows = len(data)
        cols = len(data[0]) if data else 0

        # 清除坐标轴
        ax.set_xlim(0, cols)
        ax.set_ylim(0, rows)
        ax.set_aspect('equal')

        # 隐藏坐标轴
        ax.axis('off')

        # 绘制表格
        cell_height = 1
        cell_width = 1

        for i, row_data in enumerate(data):
            for j, cell_value in enumerate(row_data):
                # 计算单元格位置
                x = j * cell_width
                y = (rows - i - 1) * cell_height

                # 绘制单元格边框
                if self.show_grid_var.get():
                    rect = patches.Rectangle((x, y), cell_width, cell_height,
                                           linewidth=1, edgecolor='black', facecolor='white')
                    ax.add_patch(rect)

                # 添加文本
                if cell_value:
                    ax.text(x + cell_width/2, y + cell_height/2, str(cell_value),
                           ha='center', va='center', fontsize=self.font_size_var.get(),
                           wrap=True)

    def generate_image(self):
        """生成图片文件"""
        if not self.selected_range:
            messagebox.showwarning("警告", "请先选择表格区域")
            return

        data = self.get_selected_data()
        if not data:
            return

        # 选择保存路径
        file_types = [("PNG图片", "*.png"), ("JPG图片", "*.jpg"), ("PDF文件", "*.pdf")]
        default_ext = self.format_var.get().lower()

        save_path = filedialog.asksaveasfilename(
            title="保存图片",
            defaultextension=f".{default_ext}",
            filetypes=file_types
        )

        if not save_path:
            return

        try:
            self.status_var.set("正在生成图片...")

            # 创建图片
            if self.format_var.get() == "PDF":
                self.generate_pdf_image(data, save_path)
            else:
                self.generate_raster_image(data, save_path)

            self.status_var.set("图片生成完成")
            messagebox.showinfo("成功", f"图片已保存到：{save_path}")

        except Exception as e:
            messagebox.showerror("错误", f"生成图片失败：{str(e)}")
            self.status_var.set("生成失败")

    def generate_raster_image(self, data, save_path):
        """生成栅格图片（PNG/JPG）"""
        if not data:
            return

        rows = len(data)
        cols = len(data[0]) if data else 0

        # 计算图片尺寸
        font_size = self.font_size_var.get()
        cell_width = max(100, font_size * 8)
        cell_height = max(30, font_size * 2)

        img_width = cols * cell_width
        img_height = rows * cell_height

        # 创建图片
        img = Image.new('RGB', (img_width, img_height), 'white')
        draw = ImageDraw.Draw(img)

        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            try:
                font = ImageFont.truetype("simhei.ttf", font_size)  # 中文字体
            except:
                font = ImageFont.load_default()

        # 绘制表格
        for i, row_data in enumerate(data):
            for j, cell_value in enumerate(row_data):
                x1 = j * cell_width
                y1 = i * cell_height
                x2 = x1 + cell_width
                y2 = y1 + cell_height

                # 绘制边框
                if self.show_grid_var.get():
                    draw.rectangle([x1, y1, x2, y2], outline='black', width=1)

                # 绘制文本
                if cell_value:
                    text_x = x1 + cell_width // 2
                    text_y = y1 + cell_height // 2

                    # 获取文本尺寸
                    bbox = draw.textbbox((0, 0), str(cell_value), font=font)
                    text_width = bbox[2] - bbox[0]
                    text_height = bbox[3] - bbox[1]

                    # 居中显示
                    draw.text((text_x - text_width//2, text_y - text_height//2),
                             str(cell_value), fill='black', font=font)

        # 保存图片
        if self.format_var.get() == "JPG":
            img.save(save_path, "JPEG", quality=95)
        else:
            img.save(save_path, "PNG")

    def generate_pdf_image(self, data, save_path):
        """生成PDF图片"""
        try:
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Table, TableStyle
            from reportlab.lib import colors
            from reportlab.lib.units import inch
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont

            # 注册中文字体
            try:
                pdfmetrics.registerFont(TTFont('SimHei', 'simhei.ttf'))
                font_name = 'SimHei'
            except:
                font_name = 'Helvetica'

            # 创建PDF文档
            doc = SimpleDocTemplate(save_path, pagesize=A4)
            elements = []

            # 创建表格
            table = Table(data)

            # 设置表格样式
            style = TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), font_name),
                ('FONTSIZE', (0, 0), (-1, -1), self.font_size_var.get()),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('BACKGROUND', (0, 0), (-1, -1), colors.white),
            ])

            if self.show_grid_var.get():
                style.add('GRID', (0, 0), (-1, -1), 1, colors.black)

            if self.include_header_var.get() and len(data) > 0:
                style.add('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey)
                style.add('FONTNAME', (0, 0), (-1, 0), font_name)
                style.add('FONTSIZE', (0, 0), (-1, 0), self.font_size_var.get() + 2)

            table.setStyle(style)
            elements.append(table)

            # 生成PDF
            doc.build(elements)

        except ImportError:
            # 如果没有reportlab，使用matplotlib生成PDF
            fig, ax = plt.subplots(figsize=(11, 8.5))
            self.create_table_image(ax, data)
            fig.savefig(save_path, format='pdf', bbox_inches='tight', dpi=self.quality_var.get())
            plt.close(fig)

    def batch_process(self):
        """批量处理功能"""
        if not self.worksheet:
            messagebox.showwarning("警告", "请先加载Excel文件")
            return

        # 创建批量处理窗口
        batch_window = tk.Toplevel(self.master)
        batch_window.title("批量处理")
        batch_window.geometry("500x400")

        # 批量设置区域
        settings_frame = ttk.LabelFrame(batch_window, text="批量设置", padding=10)
        settings_frame.pack(fill=tk.X, padx=10, pady=10)

        # 区域列表
        ttk.Label(settings_frame, text="要处理的区域（每行一个，格式：A1:C5）:").pack(anchor=tk.W)

        ranges_text = tk.Text(settings_frame, height=8, width=50)
        ranges_text.pack(fill=tk.BOTH, expand=True, pady=5)

        # 示例文本
        example_text = """A1:E10
G1:K10
A15:E25"""
        ranges_text.insert(tk.END, example_text)

        # 输出设置
        output_frame = ttk.LabelFrame(batch_window, text="输出设置", padding=10)
        output_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(output_frame, text="输出文件夹:").pack(anchor=tk.W)

        folder_frame = ttk.Frame(output_frame)
        folder_frame.pack(fill=tk.X, pady=5)

        output_folder_var = tk.StringVar()
        ttk.Entry(folder_frame, textvariable=output_folder_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(folder_frame, text="浏览",
                  command=lambda: output_folder_var.set(filedialog.askdirectory())).pack(side=tk.RIGHT, padx=(5, 0))

        # 处理按钮
        def process_batch():
            ranges_text_content = ranges_text.get(1.0, tk.END).strip()
            output_folder = output_folder_var.get().strip()

            if not ranges_text_content or not output_folder:
                messagebox.showwarning("警告", "请填写所有必要信息")
                return

            ranges = [r.strip() for r in ranges_text_content.split('\n') if r.strip()]

            try:
                for i, range_str in enumerate(ranges):
                    # 解析范围
                    if ':' not in range_str:
                        continue

                    start_cell, end_cell = range_str.split(':')

                    # 临时设置选择区域
                    self.start_cell_var.set(start_cell.strip())
                    self.end_cell_var.set(end_cell.strip())
                    self.manual_set_range()

                    # 获取数据
                    data = self.get_selected_data()
                    if not data:
                        continue

                    # 生成文件名
                    filename = f"table_{i+1}_{range_str.replace(':', '_')}.{self.format_var.get().lower()}"
                    save_path = os.path.join(output_folder, filename)

                    # 生成图片
                    if self.format_var.get() == "PDF":
                        self.generate_pdf_image(data, save_path)
                    else:
                        self.generate_raster_image(data, save_path)

                messagebox.showinfo("成功", f"批量处理完成！共处理 {len(ranges)} 个区域")
                batch_window.destroy()

            except Exception as e:
                messagebox.showerror("错误", f"批量处理失败：{str(e)}")

        ttk.Button(batch_window, text="开始批量处理", command=process_batch).pack(pady=10)

if __name__ == "__main__":
    root = tk.Tk()
    app = ExcelToImageConverter(root)
    root.mainloop()
