#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WPS/Excel表格区域截图工具 - 演示脚本
展示如何使用不同的截图方式
"""

import tkinter as tk
from tkinter import messagebox
import time
import os

def demo_clipboard_method():
    """演示剪贴板方式"""
    print("=== 剪贴板方式演示 ===")
    print("1. 请打开WPS或Excel")
    print("2. 选择一个表格区域")
    print("3. 按Ctrl+C复制")
    print("4. 然后按回车继续...")
    input()
    
    try:
        from excel_to_image import WPSExcelImageCapture
        
        # 创建临时GUI来测试剪贴板功能
        root = tk.Tk()
        root.withdraw()
        
        app = WPSExcelImageCapture(root)
        app.method_var.set("clipboard")
        
        # 模拟获取剪贴板数据
        try:
            text_data = root.clipboard_get()
            app.parse_clipboard_text(text_data)
            
            if app.clipboard_data:
                print(f"✓ 成功获取剪贴板数据: {len(app.clipboard_data)}行")
                
                # 生成图片
                image = app.generate_table_image()
                if image:
                    # 保存演示图片
                    demo_path = "demo_clipboard.png"
                    image.save(demo_path)
                    print(f"✓ 演示图片已保存: {demo_path}")
                else:
                    print("✗ 生成图片失败")
            else:
                print("✗ 剪贴板中没有找到表格数据")
                
        except tk.TclError:
            print("✗ 剪贴板为空或无法访问")
            
        root.destroy()
        
    except Exception as e:
        print(f"✗ 演示失败: {e}")

def demo_screenshot_method():
    """演示屏幕截图方式"""
    print("\n=== 屏幕截图方式演示 ===")
    print("1. 请准备好要截图的内容")
    print("2. 按回车开始截图演示...")
    input()
    
    try:
        from PIL import ImageGrab
        
        print("3秒后开始截图，请准备...")
        for i in range(3, 0, -1):
            print(f"{i}...")
            time.sleep(1)
        
        # 截取整个屏幕
        screenshot = ImageGrab.grab()
        
        # 保存演示截图
        demo_path = "demo_screenshot.png"
        screenshot.save(demo_path)
        print(f"✓ 屏幕截图已保存: {demo_path}")
        
        # 显示截图信息
        print(f"截图尺寸: {screenshot.size}")
        
    except Exception as e:
        print(f"✗ 截图演示失败: {e}")

def demo_com_method():
    """演示COM接口方式"""
    print("\n=== COM接口方式演示 ===")
    
    try:
        import win32com.client
        print("✓ pywin32库可用")
        
        print("1. 请打开Excel或WPS")
        print("2. 选择一个表格区域")
        print("3. 按回车继续...")
        input()
        
        # 尝试连接到Excel
        try:
            excel_app = win32com.client.GetActiveObject("Excel.Application")
            print("✓ 找到运行中的Excel")
            app_name = "Excel"
        except:
            try:
                wps_app = win32com.client.GetActiveObject("ET.Application")
                excel_app = wps_app
                print("✓ 找到运行中的WPS表格")
                app_name = "WPS"
            except:
                print("✗ 未找到运行中的Excel或WPS")
                return
        
        # 获取选中区域
        selection = excel_app.Selection
        if selection:
            print(f"✓ 获取到{app_name}中的选中区域")
            
            # 尝试获取选中区域的地址
            try:
                address = selection.Address
                print(f"选中区域地址: {address}")
            except:
                print("无法获取区域地址")
            
            # 尝试获取数据
            try:
                if hasattr(selection, 'Value'):
                    value = selection.Value
                    if value:
                        print(f"✓ 获取到数据，类型: {type(value)}")
                        if isinstance(value, (list, tuple)):
                            print(f"数据行数: {len(value)}")
                        else:
                            print(f"数据值: {value}")
                    else:
                        print("选中区域为空")
                else:
                    print("无法获取选中区域的值")
            except Exception as e:
                print(f"获取数据时出错: {e}")
        else:
            print("✗ 没有选中任何区域")
            
    except ImportError:
        print("✗ pywin32库未安装，COM接口不可用")
    except Exception as e:
        print(f"✗ COM接口演示失败: {e}")

def demo_hotkey_setup():
    """演示热键设置"""
    print("\n=== 热键功能演示 ===")
    
    try:
        import keyboard
        print("✓ keyboard库可用")
        
        print("设置热键演示...")
        print("注意：这只是演示，实际热键功能需要在主程序中使用")
        
        def test_hotkey():
            print("热键被触发！")
        
        print("尝试设置测试热键 Ctrl+Shift+T...")
        keyboard.add_hotkey('ctrl+shift+t', test_hotkey)
        print("✓ 热键设置成功")
        print("按Ctrl+Shift+T测试热键，按ESC退出演示")
        
        # 等待ESC键退出
        keyboard.wait('esc')
        print("演示结束")
        
    except ImportError:
        print("✗ keyboard库未安装，热键功能不可用")
    except Exception as e:
        print(f"✗ 热键演示失败: {e}")

def main():
    """主演示函数"""
    print("WPS/Excel表格区域截图工具 - 功能演示")
    print("=" * 50)
    
    demos = [
        ("1. 剪贴板方式", demo_clipboard_method),
        ("2. 屏幕截图方式", demo_screenshot_method),
        ("3. COM接口方式", demo_com_method),
        ("4. 热键功能", demo_hotkey_setup)
    ]
    
    while True:
        print("\n请选择要演示的功能:")
        for i, (name, _) in enumerate(demos):
            print(f"{name}")
        print("0. 退出演示")
        
        try:
            choice = input("\n请输入选择 (0-4): ").strip()
            
            if choice == "0":
                print("演示结束，再见！")
                break
            elif choice in ["1", "2", "3", "4"]:
                idx = int(choice) - 1
                name, demo_func = demos[idx]
                print(f"\n开始演示: {name}")
                demo_func()
            else:
                print("无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n演示被中断")
            break
        except Exception as e:
            print(f"演示出错: {e}")

if __name__ == "__main__":
    main()
