#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跨平台表格截图工具 - 无界面热键版本
支持剪贴板图片和手动截图，使用热键控制

热键说明：
- Ctrl+Alt+1: 从剪贴板获取图片并保存
- Ctrl+Alt+2: 手动截图并保存
- Ctrl+Alt+Q: 退出程序
"""

from PIL import Image, ImageGrab
import os
import time
from datetime import datetime
import platform
import tempfile
import threading

# 检测操作系统
SYSTEM_OS = platform.system()
print(f"🖥️  检测到操作系统: {SYSTEM_OS}")

# 导入热键库
try:
    import keyboard
    KEYBOARD_AVAILABLE = True
    print("✅ keyboard 库已加载")
    if SYSTEM_OS == "Linux":
        print("⚠️  Linux系统可能需要root权限使用热键功能")
except ImportError:
    KEYBOARD_AVAILABLE = False
    print("❌ keyboard 库未安装，程序无法运行")
    print("请安装: pip install keyboard")
    exit(1)

# 跨平台剪贴板支持
try:
    if SYSTEM_OS == "Linux":
        import subprocess
        print("✅ Linux剪贴板支持已启用（需要xclip）")
    elif SYSTEM_OS == "Darwin":  # macOS
        import subprocess
        print("✅ macOS剪贴板支持已启用（需要pngpaste）")
    else:  # Windows
        print("✅ Windows剪贴板支持已启用")
    CLIPBOARD_AVAILABLE = True
except ImportError:
    CLIPBOARD_AVAILABLE = False
    print("❌ 剪贴板功能不可用")


class CrossPlatformScreenshot:
    def __init__(self):
        self.running = True
        self.screenshot_image = None
        self.save_directory = os.path.expanduser("~/Desktop")  # 默认保存到桌面
        
        # 确保保存目录存在
        if not os.path.exists(self.save_directory):
            os.makedirs(self.save_directory)
            
        print(f"📁 图片保存目录: {self.save_directory}")
        
    def get_image_from_clipboard(self):
        """跨平台从剪贴板获取图片"""
        try:
            if SYSTEM_OS == "Windows":
                # Windows系统使用PIL的ImageGrab
                image = ImageGrab.grabclipboard()
                return image
                
            elif SYSTEM_OS == "Linux":
                # Linux系统使用xclip
                try:
                    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                        result = subprocess.run([
                            'xclip', '-selection', 'clipboard', '-t', 'image/png', '-o'
                        ], stdout=tmp_file, stderr=subprocess.PIPE)
                        
                        if result.returncode == 0:
                            image = Image.open(tmp_file.name)
                            os.unlink(tmp_file.name)  # 删除临时文件
                            return image
                        else:
                            print("❌ Linux剪贴板中没有图片数据")
                            return None
                            
                except FileNotFoundError:
                    print("❌ Linux系统需要安装xclip: sudo apt-get install xclip")
                    return None
                    
            elif SYSTEM_OS == "Darwin":  # macOS
                # macOS系统使用pngpaste
                try:
                    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                        result = subprocess.run([
                            'pngpaste', tmp_file.name
                        ], stderr=subprocess.PIPE)
                        
                        if result.returncode == 0:
                            image = Image.open(tmp_file.name)
                            os.unlink(tmp_file.name)
                            return image
                        else:
                            print("❌ macOS剪贴板中没有图片数据")
                            return None
                            
                except FileNotFoundError:
                    print("❌ macOS系统需要安装pngpaste: brew install pngpaste")
                    return None
            
            return None
            
        except Exception as e:
            print(f"❌ 从剪贴板获取图片失败: {e}")
            return None
    
    def capture_screenshot(self):
        """手动截图"""
        print("📸 准备手动截图...")
        print("⏳ 3秒后开始截图，请准备...")
        
        for i in range(3, 0, -1):
            print(f"⏰ {i}...")
            time.sleep(1)
        
        try:
            # 截取整个屏幕
            screenshot = ImageGrab.grab()
            print("✅ 屏幕截图完成")
            return screenshot
            
        except Exception as e:
            print(f"❌ 截图失败: {e}")
            return None
    
    def save_image(self, image, prefix="screenshot"):
        """保存图片"""
        if not image:
            print("❌ 没有可保存的图片")
            return False
            
        try:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{prefix}_{timestamp}.png"
            filepath = os.path.join(self.save_directory, filename)
            
            # 保存图片
            image.save(filepath, "PNG")
            print(f"✅ 图片已保存: {filepath}")
            print(f"📏 图片尺寸: {image.size[0]}x{image.size[1]}")
            return True
            
        except Exception as e:
            print(f"❌ 保存图片失败: {e}")
            return False
    
    def handle_clipboard_capture(self):
        """处理剪贴板截图"""
        print("\n🎯 开始从剪贴板获取图片...")
        
        image = self.get_image_from_clipboard()
        if image:
            self.save_image(image, "clipboard")
        else:
            print("💡 提示：请先在表格软件中复制图片到剪贴板")
            if SYSTEM_OS == "Windows":
                print("   - 在WPS/Excel中右键选择'复制为图片'")
            elif SYSTEM_OS == "Linux":
                print("   - 在LibreOffice中复制为图片")
            elif SYSTEM_OS == "Darwin":
                print("   - 在Numbers/Excel中复制为图片")
    
    def handle_manual_screenshot(self):
        """处理手动截图"""
        print("\n🎯 开始手动截图...")
        
        image = self.capture_screenshot()
        if image:
            self.save_image(image, "manual")
    
    def setup_hotkeys(self):
        """设置热键"""
        print("\n⌨️  设置热键...")
        
        # 剪贴板截图
        keyboard.add_hotkey('ctrl+alt+1', self.handle_clipboard_capture)
        print("✅ Ctrl+Alt+1: 从剪贴板获取图片")
        
        # 手动截图
        keyboard.add_hotkey('ctrl+alt+2', self.handle_manual_screenshot)
        print("✅ Ctrl+Alt+2: 手动截图")
        
        # 退出程序
        keyboard.add_hotkey('ctrl+alt+q', self.quit_program)
        print("✅ Ctrl+Alt+Q: 退出程序")
        
    def quit_program(self):
        """退出程序"""
        print("\n👋 程序退出中...")
        self.running = False
        
    def run(self):
        """运行程序"""
        print("\n" + "="*50)
        print("🚀 跨平台表格截图工具已启动")
        print("="*50)
        
        if not KEYBOARD_AVAILABLE:
            print("❌ 热键功能不可用，程序退出")
            return
            
        self.setup_hotkeys()
        
        print(f"\n📋 使用说明:")
        print(f"1️⃣  Ctrl+Alt+1 - 从剪贴板获取图片并保存")
        print(f"2️⃣  Ctrl+Alt+2 - 手动截图并保存")
        print(f"❌ Ctrl+Alt+Q - 退出程序")
        print(f"\n💾 图片保存位置: {self.save_directory}")
        print(f"\n⏳ 程序运行中，按热键操作...")
        
        try:
            while self.running:
                time.sleep(0.1)
        except KeyboardInterrupt:
            print("\n👋 程序被用户中断")
        finally:
            print("🔚 程序已退出")


def main():
    """主函数"""
    try:
        app = CrossPlatformScreenshot()
        app.run()
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        input("按回车键退出...")


if __name__ == "__main__":
    main()
