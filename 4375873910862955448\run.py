#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel表格区域生成图片工具 - 启动脚本
"""

import sys
import os
import subprocess
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """检查依赖库是否安装"""
    required_packages = [
        'pandas',
        'openpyxl', 
        'matplotlib',
        'PIL',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies(packages):
    """安装缺失的依赖库"""
    try:
        for package in packages:
            if package == 'PIL':
                package = 'Pillow'
            print(f"正在安装 {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        return True
    except subprocess.CalledProcessError as e:
        print(f"安装失败: {e}")
        return False

def main():
    """主函数"""
    print("Excel表格区域生成图片工具")
    print("=" * 40)
    
    # 检查依赖
    print("检查依赖库...")
    missing = check_dependencies()
    
    if missing:
        print(f"缺少以下依赖库: {', '.join(missing)}")
        
        # 创建简单的GUI询问是否安装
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        result = messagebox.askyesno(
            "缺少依赖库",
            f"缺少以下依赖库:\n{chr(10).join(missing)}\n\n是否现在安装？"
        )
        
        if result:
            print("正在安装依赖库...")
            if install_dependencies(missing):
                print("依赖库安装完成！")
            else:
                print("依赖库安装失败，请手动安装：")
                print(f"pip install {' '.join(missing)}")
                input("按回车键退出...")
                return
        else:
            print("请手动安装依赖库后再运行程序：")
            print(f"pip install {' '.join(missing)}")
            input("按回车键退出...")
            return
        
        root.destroy()
    
    # 启动主程序
    try:
        print("启动程序...")
        from excel_to_image import ExcelToImageConverter
        
        root = tk.Tk()
        app = ExcelToImageConverter(root)
        
        # 设置窗口图标（如果有的话）
        try:
            root.iconbitmap('icon.ico')
        except:
            pass
        
        print("程序已启动！")
        root.mainloop()
        
    except ImportError as e:
        print(f"导入模块失败: {e}")
        print("请确保 excel_to_image.py 文件在当前目录下")
        input("按回车键退出...")
    except Exception as e:
        print(f"程序启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
